#!/bin/bash

echo "Cleaning up any existing services..."

# Function to forcefully kill process by port
force_kill_by_port() {
    local port=$1
    local pid=$(lsof -ti:$port)
    if [ ! -z "$pid" ]; then
        echo "Forcefully stopping process on port $port (PID: $pid)"
        kill -9 $pid 2>/dev/null
    fi
}

# Kill backend (port 8000)
force_kill_by_port 8000

# Kill frontend (port 5173 - default Vite port)
force_kill_by_port 5173

# Kill any remaining node processes
echo "Cleaning up any remaining Node.js processes..."
pkill -9 -f "node.*vite" 2>/dev/null
pkill -9 -f "uvicorn" 2>/dev/null

echo "Cleanup complete. All services have been forcefully terminated." 