from pydantic_settings import BaseSettings
from functools import lru_cache
from typing import Optional

class Settings(BaseSettings):
    # Application
    APP_NAME: str = "AI Prompt Playground"
    DEBUG: bool = False
    VERSION: str = "0.1.0"
    
    # Security
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Microsoft SSO
    MICROSOFT_CLIENT_ID: str
    MICROSOFT_CLIENT_SECRET: str
    MICROSOFT_TENANT_ID: str
    MICROSOFT_REDIRECT_URI: str
    
    # AWS
    AWS_ACCESS_KEY_ID: str
    AWS_SECRET_ACCESS_KEY: str
    AWS_REGION: str = "us-east-1"
    
    # MongoDB
    MONGODB_URL: str
    MONGODB_DB_NAME: str = "ai_prompt_playground"
    
    # SOLR
    SOLR_URL: str
    SOLR_USERNAME: str = "insights_app"
    SOLR_PASSWORD: str

    # Redis
    REDIS_URL: Optional[str] = None

    # Domain restriction
    ALLOWED_DOMAIN: Optional[str] = None
    
    # Default Admin for DB initialization (optional)
    DEFAULT_ADMIN_EMAIL: Optional[str] = None
    DEFAULT_ADMIN_NAME: Optional[str] = None

    # LLM API Keys
    OPENAI_API_KEY: str
    ANTHROPIC_API_KEY: str
    BEDROCK_ACCESS_KEY: str
    BEDROCK_SECRET_KEY: str

    # Model Configuration
    DEFAULT_MODEL: str = "gpt-4o-mini"
    MAX_TOKENS: int = 4096
    TEMPERATURE: float = 0.7

    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 3600  # in seconds
    
    class Config:
        env_file = ".env"
        case_sensitive = True

@lru_cache()
def get_settings() -> Settings:
    return Settings() 