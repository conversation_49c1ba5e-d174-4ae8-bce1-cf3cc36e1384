import json
from typing import Any, Optional
import aioredis
from app.config import get_settings

settings = get_settings()

class CacheService:
    def __init__(self):
        self.redis = None
        self.default_ttl = 3600  # 1 hour in seconds

    async def init_redis(self):
        if not self.redis:
            self.redis = await aioredis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        await self.init_redis()
        value = await self.redis.get(key)
        if value:
            return json.loads(value)
        return None

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache with optional TTL."""
        await self.init_redis()
        try:
            await self.redis.set(
                key,
                json.dumps(value),
                ex=ttl or self.default_ttl
            )
            return True
        except Exception:
            return False

    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        await self.init_redis()
        return bool(await self.redis.delete(key))

    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern."""
        await self.init_redis()
        keys = await self.redis.keys(pattern)
        if keys:
            return await self.redis.delete(*keys)
        return 0

cache_service = CacheService() 