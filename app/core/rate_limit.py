from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
import time
from typing import Optional, <PERSON><PERSON>
import aioredis
from app.config import get_settings

settings = get_settings()

class RateLimiter:
    def __init__(self):
        self.redis = None
        self.default_limit = 999999999  # requests per window - Set a very high limit for development
        self.default_window = 3600  # 1 hour in seconds

    async def init_redis(self):
        if not self.redis:
            self.redis = await aioredis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )

    async def get_rate_limit(self, key: str) -> Tuple[int, int]:
        """Get current rate limit status for a key."""
        await self.init_redis()
        current = await self.redis.get(key)
        if current:
            return int(current), self.default_window
        return 0, self.default_window

    async def increment(self, key: str) -> Tuple[int, int]:
        """Increment the rate limit counter for a key."""
        await self.init_redis()
        pipe = self.redis.pipeline()
        now = int(time.time())
        window_key = f"{key}:{now // self.default_window}"
        
        # Increment counter
        pipe.incr(window_key)
        # Set expiry
        pipe.expire(window_key, self.default_window)
        # Get current count
        pipe.get(window_key)
        
        results = await pipe.execute()
        current = int(results[2] or 0)
        
        return current, self.default_window

rate_limiter = RateLimiter()

async def rate_limit_middleware(request: Request, call_next):
    # Skip rate limiting for certain paths
    if request.url.path in ["/health", "/docs", "/redoc", "/openapi.json"]:
        return await call_next(request)

    # Get client identifier (IP or user ID if authenticated)
    client_id = request.client.host
    if hasattr(request.state, "user"):
        client_id = request.state.user["email"]

    # Create rate limit key
    key = f"rate_limit:{client_id}"

    # Check and increment rate limit
    current, window = await rate_limiter.increment(key)

    # Add rate limit headers
    response = await call_next(request)
    response.headers["X-RateLimit-Limit"] = str(rate_limiter.default_limit)
    response.headers["X-RateLimit-Remaining"] = str(max(0, rate_limiter.default_limit - current))
    response.headers["X-RateLimit-Reset"] = str(int(time.time()) + window)

    # Check if rate limit exceeded
    if current > rate_limiter.default_limit:
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "detail": "Rate limit exceeded",
                "retry_after": window
            },
            headers={
                "Retry-After": str(window)
            }
        )

    return response 