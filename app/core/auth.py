from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON>uthorization<PERSON><PERSON><PERSON>earer
from jose import JW<PERSON>rror, jwt
from datetime import datetime, timedelta
from typing import Optional
from app.config import get_settings

settings = get_settings()

oauth2_scheme = OAuth2AuthorizationCodeBearer(
    authorizationUrl="https://login.microsoftonline.com/common/oauth2/v2.0/authorize",
    tokenUrl="https://login.microsoftonline.com/common/oauth2/v2.0/token"
)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        roles = payload.get("roles", ["user"])
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    return {"email": username, "roles": roles}

def require_roles(required_roles: list):
    def role_checker(user = Depends(get_current_user)):
        user_roles = user["roles"]
        if not any(role in user_roles for role in required_roles):
            raise HTTPException(status_code=403, detail="Insufficient role permissions")
        return user
    return role_checker 