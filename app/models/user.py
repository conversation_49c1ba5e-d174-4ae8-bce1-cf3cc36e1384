from pydantic import BaseModel, EmailStr, Field, computed_field, model_validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from bson import ObjectId # Import ObjectId

class UserBase(BaseModel):
    email: EmailStr
    full_name: str
    is_active: bool = True
    is_superuser: bool = False
    roles: list = ["user"]

class UserCreate(UserBase):
    pass

class UserUpdate(UserBase):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None
    is_superuser: Optional[bool] = None

class UserInDB(UserBase):
    id: str # Make id required here
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    preferences: Dict[str, Any] = {}

    @model_validator(mode='before')
    @classmethod
    def set_id_from_mongo_id(cls, data: Any) -> Any:
        if isinstance(data, dict) and '_id' in data and 'id' not in data:
            data['id'] = str(data['_id'])
        return data

    class Config:
        from_attributes = True
        populate_by_name = True
        arbitrary_types_allowed = True # Allow ObjectId type

class User(UserBase):
    id: str # Make id required here
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None

    @model_validator(mode='before')
    @classmethod
    def set_id_from_mongo_id(cls, data: Any) -> Any:
        if isinstance(data, dict) and '_id' in data and 'id' not in data:
            data['id'] = str(data['_id'])
        return data

    class Config:
        from_attributes = True
        populate_by_name = True
        arbitrary_types_allowed = True # Allow ObjectId type 