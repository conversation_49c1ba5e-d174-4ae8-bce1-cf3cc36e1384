from pydantic import BaseModel, Field
from typing import Optional, List, Dict
from datetime import datetime

class PromptBase(BaseModel):
    title: str
    content: str
    model: str
    system_prompt: Optional[str] = None
    parameters: Dict = Field(default_factory=dict)
    is_template: bool = False
    is_public: bool = False

class PromptCreate(PromptBase):
    pass

class PromptUpdate(PromptBase):
    title: Optional[str] = None
    content: Optional[str] = None
    model: Optional[str] = None
    system_prompt: Optional[str] = None
    parameters: Optional[Dict] = None
    is_template: Optional[bool] = None
    is_public: Optional[bool] = None

class PromptInDB(PromptBase):
    id: str
    created_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    usage_count: int = 0
    average_tokens: Optional[float] = 0.0
    tags: List[str] = Field(default_factory=list)

class Prompt(PromptBase):
    id: str
    created_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    usage_count: int = 0
    average_tokens: Optional[float] = 0.0
    tags: List[str] = Field(default_factory=list)

    class Config:
        from_attributes = True

class PromptVersion(BaseModel):
    prompt_id: str
    version: int
    prompt: str
    labels: List[str]
    created_at: datetime = Field(default_factory=datetime.utcnow)
    is_production: bool = False

class Project(BaseModel):
    name: str
    description: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow) 