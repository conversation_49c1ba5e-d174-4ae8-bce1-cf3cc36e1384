from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
from datetime import datetime

from app.services.prompt import prompt_service
from app.services.solr import get_patent_data
from app.core.auth import get_current_user
from app.models.prompt import PromptCreate, PromptUpdate, Project

router = APIRouter(prefix="/api/playground", tags=["playground"])

@router.get("/projects")
async def get_projects(current_user = Depends(get_current_user)):
    """Get list of all projects"""
    return await prompt_service.get_list_of_projects()

@router.get("/prompts")
async def get_prompts(current_user = Depends(get_current_user)):
    """Get list of all prompts"""
    return await prompt_service.get_list_of_prompts()

@router.get("/prompts/{prompt_name}/latest")
async def get_latest_prompt(prompt_name: str, current_user = Depends(get_current_user)):
    """Get latest version of a prompt"""
    prompt, version = await prompt_service.get_latest_prompt(prompt_name)
    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    return {"prompt": prompt, "version": version}

@router.get("/prompts/{prompt_name}/version/{version}")
async def get_prompt_version(prompt_name: str, version: int, current_user = Depends(get_current_user)):
    """Get specific version of a prompt"""
    prompt, tags = await prompt_service.get_prompt_version(prompt_name, version)
    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt version not found")
    return {"prompt": prompt, "tags": tags}

@router.post("/prompts")
async def create_prompt(prompt: PromptCreate, current_user = Depends(get_current_user)):
    """Create a new prompt"""
    dt_object = datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
    tags = [dt_object, "latest"]
    await prompt_service.add_or_update_prompt(
        prompt_name=prompt.title,
        content=prompt.content,
        tags=tags,
        model=prompt.model
    )
    return {"status": "success"}

@router.put("/prompts/{prompt_id}")
async def update_prompt(
    prompt_id: str,
    prompt_update: PromptUpdate,
    current_user = Depends(get_current_user)
):
    """Update an existing prompt"""
    updated_prompt = await prompt_service.update_prompt(prompt_id, prompt_update)
    if not updated_prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    return updated_prompt

@router.post("/projects")
async def create_project(project: Project, current_user = Depends(get_current_user)):
    """Create a new project"""
    project_id = await prompt_service.create_project(project.name, project.description)
    return {"project_id": project_id}

@router.post("/patents")
async def get_patents(patnums: List[str], current_user = Depends(get_current_user)):
    """Get patent data from Solr"""
    try:
        data = await get_patent_data(patnums)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 