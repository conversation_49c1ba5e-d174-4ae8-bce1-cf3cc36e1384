from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
from app.core.auth import get_current_user
from app.services.bedrock import bedrock_service
from app.services.solr import solr_service
from app.db.mongodb import mongodb
from datetime import datetime

router = APIRouter(prefix="/chat", tags=["chat"])

class ChatRequest(BaseModel):
    message: str
    model_id: str
    system_prompt: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    solr_collection: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    token_usage: Optional[int] = None
    solr_fields: Optional[Any] = None

@router.post("/", response_model=ChatResponse)
async def chat(
    chat_req: ChatRequest,
    user = Depends(get_current_user)
):
    # Optionally fetch SOLR fields
    solr_fields = None
    if chat_req.solr_collection:
        try:
            solr_fields = solr_service.get_dynamic_fields(chat_req.solr_collection)
        except Exception as e:
            solr_fields = {"error": str(e)}
    # Call Bedrock LLM
    try:
        response = bedrock_service.invoke_model(
            model_id=chat_req.model_id,
            prompt=chat_req.message,
            system_prompt=chat_req.system_prompt,
            parameters=chat_req.parameters
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Bedrock error: {e}")
    # Token usage (placeholder, real calculation depends on model)
    token_usage = len(chat_req.message.split()) + len(str(response).split())
    # Log interaction
    log = {
        "user": user["email"],
        "model_id": chat_req.model_id,
        "prompt": chat_req.message,
        "system_prompt": chat_req.system_prompt,
        "parameters": chat_req.parameters,
        "response": response,
        "token_usage": token_usage,
        "solr_collection": chat_req.solr_collection,
        "solr_fields": solr_fields,
        "timestamp": datetime.utcnow()
    }
    await mongodb.get_collection("chats").insert_one(log)
    return ChatResponse(response=response, token_usage=token_usage, solr_fields=solr_fields) 