from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from bson import ObjectId # For handling MongoDB _id
from datetime import datetime

from app.models.user import UserInDB, UserCreate, UserUpdate, User
from app.db.mongodb import mongodb
from app.dependencies import get_current_active_superuser, get_current_active_user

router = APIRouter(prefix="/users", tags=["users"])

@router.get("/me", response_model=User)
async def read_users_me(current_user: UserInDB = Depends(get_current_active_user)):
    """
    Get current authenticated user's details.
    """
    return User(**current_user.model_dump())

@router.get("/", response_model=List[User])
async def read_users(
    skip: int = 0,
    limit: int = 100,
    current_user: UserInDB = Depends(get_current_active_superuser),
):
    """
    Retrieve a list of all users. Accessible only by superusers.
    """
    users_cursor = mongodb.get_collection("users").find({}).skip(skip).limit(limit)
    users_list = []
    async for user_doc in users_cursor:
        user_doc['id'] = str(user_doc['_id'])
        users_list.append(User(**user_doc))
    return users_list

@router.post("/", response_model=User, status_code=status.HTTP_201_CREATED)
async def create_user(
    user: UserCreate,
    current_user: UserInDB = Depends(get_current_active_superuser),
):
    """
    Create a new user. Accessible only by superusers.
    """
    # Check if user with this email already exists
    if await mongodb.get_collection("users").find_one({"email": user.email}):
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # In a real application, you would hash the password here if users had passwords
    # For SSO, we're assuming password management is handled by the SSO provider.

    new_user_data = user.model_dump()
    new_user_data["created_at"] = new_user_data["updated_at"] = datetime.utcnow()
    
    # Insert into MongoDB
    result = await mongodb.get_collection("users").insert_one(new_user_data)
    
    created_user_doc = await mongodb.get_collection("users").find_one({"_id": result.inserted_id})
    if not created_user_doc:
        raise HTTPException(status_code=500, detail="Failed to create user")
    
    created_user_doc['id'] = str(created_user_doc['_id'])
    return User(**created_user_doc)

@router.get("/{user_id}", response_model=User)
async def read_user_by_id(
    user_id: str,
    current_user: UserInDB = Depends(get_current_active_superuser),
):
    """
    Retrieve a user by ID. Accessible only by superusers.
    """
    if not ObjectId.is_valid(user_id):
        raise HTTPException(status_code=400, detail="Invalid User ID format")

    user_doc = await mongodb.get_collection("users").find_one({"_id": ObjectId(user_id)})
    if not user_doc:
        raise HTTPException(status_code=404, detail="User not found")
    user_doc['id'] = str(user_doc['_id'])
    return User(**user_doc)

@router.put("/{user_id}", response_model=User)
async def update_user(
    user_id: str,
    user_update: UserUpdate,
    current_user: UserInDB = Depends(get_current_active_superuser),
):
    """
    Update an existing user by ID. Accessible only by superusers.
    """
    if not ObjectId.is_valid(user_id):
        raise HTTPException(status_code=400, detail="Invalid User ID format")

    existing_user_doc = await mongodb.get_collection("users").find_one({"_id": ObjectId(user_id)})
    if not existing_user_doc:
        raise HTTPException(status_code=404, detail="User not found")
    
    update_data = {
        k: v for k, v in user_update.model_dump(exclude_unset=True).items() if v is not None
    }
    if "email" in update_data and await mongodb.get_collection("users").find_one({"email": update_data["email"], "_id": {"$ne": ObjectId(user_id)}}):
        raise HTTPException(status_code=400, detail="Email already registered by another user")

    update_data["updated_at"] = datetime.utcnow()

    await mongodb.get_collection("users").update_one(
        {"_id": ObjectId(user_id)},
        {"$set": update_data}
    )

    updated_user_doc = await mongodb.get_collection("users").find_one({"_id": ObjectId(user_id)})
    updated_user_doc['id'] = str(updated_user_doc['_id'])
    return User(**updated_user_doc)

@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: str,
    current_user: UserInDB = Depends(get_current_active_superuser),
):
    """
    Delete a user by ID. Accessible only by superusers.
    """
    if not ObjectId.is_valid(user_id):
        raise HTTPException(status_code=400, detail="Invalid User ID format")

    delete_result = await mongodb.get_collection("users").delete_one({"_id": ObjectId(user_id)})
    if delete_result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    return 