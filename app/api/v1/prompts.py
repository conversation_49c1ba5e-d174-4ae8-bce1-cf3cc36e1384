from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from app.models.prompt import Prompt, PromptCreate, PromptUpdate
from app.dependencies import get_current_active_user
from app.db.mongodb import mongodb
from datetime import datetime
from bson import ObjectId
from app.models.user import UserInDB

router = APIRouter(prefix="/prompts", tags=["prompts"])

@router.post("/", response_model=Prompt)
async def create_prompt(
    prompt: PromptCreate,
    current_user: UserInDB = Depends(get_current_active_user)
):
    prompt_dict = prompt.model_dump()
    prompt_dict.update({
        "created_by": current_user.email,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "usage_count": 0,
        "average_tokens": 0.0,
        "tags": [],
    })
    
    result = await mongodb.get_collection("prompts").insert_one(prompt_dict)
    created_prompt_doc = await mongodb.get_collection("prompts").find_one({"_id": result.inserted_id})
    if not created_prompt_doc:
        raise HTTPException(status_code=500, detail="Failed to create prompt")

    created_prompt_doc["id"] = str(created_prompt_doc.pop("_id"))
    return Prompt(**created_prompt_doc)

@router.get("/", response_model=List[Prompt])
async def list_prompts(
    skip: int = 0,
    limit: int = 10,
    current_user: UserInDB = Depends(get_current_active_user)
):
    cursor = mongodb.get_collection("prompts").find(
        {"$or": [
            {"created_by": current_user.email},
            {"is_public": True}
        ]}
    ).skip(skip).limit(limit)
    
    prompts = []
    async for document in cursor:
        document["id"] = str(document.pop("_id"))
        
        # Robustly handle created_by: ensure it's a string or None
        created_by_data = document.get("created_by")
        if isinstance(created_by_data, dict) and "email" in created_by_data:
            document["created_by"] = created_by_data["email"]
        elif not isinstance(created_by_data, str):
            document["created_by"] = None

        # Ensure average_tokens is a float or None
        if "average_tokens" not in document or document["average_tokens"] is None:
            document["average_tokens"] = 0.0
        
        # Ensure tags is a list of strings or an empty list
        if "tags" not in document or document["tags"] is None:
            document["tags"] = []
        elif not isinstance(document["tags"], list):
            document["tags"] = [] # Fallback if it's not a list

        prompts.append(Prompt(**document))
    return prompts

@router.get("/{prompt_id}", response_model=Prompt)
async def get_prompt(
    prompt_id: str,
    current_user: UserInDB = Depends(get_current_active_user)
):
    prompt_doc = await mongodb.get_collection("prompts").find_one({"_id": ObjectId(prompt_id)})
    if not prompt_doc:
        raise HTTPException(status_code=404, detail="Prompt not found")
    
    # Robustly handle created_by transformation for existing data
    created_by_data = prompt_doc.get("created_by")
    if isinstance(created_by_data, dict) and "email" in created_by_data:
        prompt_doc["created_by"] = created_by_data["email"]
    elif not isinstance(created_by_data, str):
        prompt_doc["created_by"] = None

    # Ensure average_tokens and tags are present, even if empty/default
    if "average_tokens" not in prompt_doc or prompt_doc["average_tokens"] is None:
        prompt_doc["average_tokens"] = 0.0
    if "tags" not in prompt_doc or prompt_doc["tags"] is None:
        prompt_doc["tags"] = []
    elif not isinstance(prompt_doc["tags"], list):
        prompt_doc["tags"] = []

    if prompt_doc["created_by"] != current_user.email and not prompt_doc["is_public"]:
        raise HTTPException(status_code=403, detail="Not authorized to access this prompt")
    
    prompt_doc["id"] = str(prompt_doc.pop("_id"))
    return Prompt(**prompt_doc)

@router.put("/{prompt_id}", response_model=Prompt)
async def update_prompt(
    prompt_id: str,
    prompt_update: PromptUpdate,
    current_user: UserInDB = Depends(get_current_active_user)
):
    prompt_doc = await mongodb.get_collection("prompts").find_one({"_id": ObjectId(prompt_id)})
    if not prompt_doc:
        raise HTTPException(status_code=404, detail="Prompt not found")
    
    # Robustly handle created_by transformation for existing data before comparison
    created_by_data = prompt_doc.get("created_by")
    if isinstance(created_by_data, dict) and "email" in created_by_data:
        prompt_doc["created_by"] = created_by_data["email"]
    elif not isinstance(created_by_data, str):
        prompt_doc["created_by"] = None

    if prompt_doc["created_by"] != current_user.email:
        raise HTTPException(status_code=403, detail="Not authorized to update this prompt")
    
    update_data = prompt_update.model_dump(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow()
    
    await mongodb.get_collection("prompts").update_one(
        {"_id": ObjectId(prompt_id)},
        {"$set": update_data}
    )
    
    updated_prompt_doc = await mongodb.get_collection("prompts").find_one({"_id": ObjectId(prompt_id)})
    updated_prompt_doc["id"] = str(updated_prompt_doc.pop("_id"))
    return Prompt(**updated_prompt_doc)

@router.delete("/{prompt_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_prompt(
    prompt_id: str,
    current_user: UserInDB = Depends(get_current_active_user)
):
    prompt_doc = await mongodb.get_collection("prompts").find_one({"_id": ObjectId(prompt_id)})
    if not prompt_doc:
        raise HTTPException(status_code=404, detail="Prompt not found")
    
    # Robustly handle created_by transformation for existing data before comparison
    created_by_data = prompt_doc.get("created_by")
    if isinstance(created_by_data, dict) and "email" in created_by_data:
        prompt_doc["created_by"] = created_by_data["email"]
    elif not isinstance(created_by_data, str):
        prompt_doc["created_by"] = None

    if prompt_doc["created_by"] != current_user.email:
        raise HTTPException(status_code=403, detail="Not authorized to delete this prompt")
    
    await mongodb.get_collection("prompts").delete_one({"_id": ObjectId(prompt_id)}) 