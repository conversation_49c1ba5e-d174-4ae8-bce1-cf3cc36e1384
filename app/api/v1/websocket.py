from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from typing import Dict, List, Any
import json
from app.core.auth import get_current_user
from app.services.bedrock import bedrock_service
from app.db.mongodb import mongodb
from datetime import datetime

router = APIRouter(prefix="/ws", tags=["websocket"])

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]

    async def send_personal_message(self, message: str, client_id: str):
        if client_id in self.active_connections:
            await self.active_connections[client_id].send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections.values():
            await connection.send_text(message)

manager = ConnectionManager()

@router.websocket("/chat/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)
    try:
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Process message with Bedrock
            try:
                response = bedrock_service.invoke_model(
                    model_id=message_data.get("model_id", "default"),
                    prompt=message_data["message"],
                    system_prompt=message_data.get("system_prompt"),
                    parameters=message_data.get("parameters", {})
                )
                
                # Log the interaction
                log = {
                    "user": client_id,
                    "model_id": message_data.get("model_id", "default"),
                    "prompt": message_data["message"],
                    "system_prompt": message_data.get("system_prompt"),
                    "parameters": message_data.get("parameters", {}),
                    "response": response,
                    "timestamp": datetime.utcnow()
                }
                await mongodb.get_collection("chats").insert_one(log)
                
                # Send response back to client
                await manager.send_personal_message(
                    json.dumps({
                        "type": "response",
                        "content": response,
                        "timestamp": datetime.utcnow().isoformat()
                    }),
                    client_id
                )
                
            except Exception as e:
                await manager.send_personal_message(
                    json.dumps({
                        "type": "error",
                        "content": str(e),
                        "timestamp": datetime.utcnow().isoformat()
                    }),
                    client_id
                )
                
    except WebSocketDisconnect:
        manager.disconnect(client_id) 