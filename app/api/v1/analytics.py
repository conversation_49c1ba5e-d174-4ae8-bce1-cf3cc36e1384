from fastapi import APIRouter, Depends, Query
from typing import Optional, List
from datetime import datetime
from app.core.auth import get_current_user, require_roles
from app.services.analytics import analytics_service

router = APIRouter(prefix="/analytics", tags=["analytics"])

@router.get("/usage")
async def get_token_usage(
    days: int = Query(30, ge=1, le=365),
    user = Depends(get_current_user)
):
    """Get token usage statistics for the current user."""
    return await analytics_service.get_user_token_usage(user["email"], days)

@router.get("/history")
async def get_chat_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    model_id: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    user = Depends(get_current_user)
):
    """Get chat history for the current user with optional filters."""
    return await analytics_service.get_chat_history(
        user["email"],
        skip,
        limit,
        model_id,
        start_date,
        end_date
    )

@router.get("/global")
async def get_global_analytics(
    days: int = Query(30, ge=1, le=365),
    user = Depends(require_roles(["admin"]))
):
    """Get global analytics across all users (admin only)."""
    return await analytics_service.get_global_analytics(days) 