from fastapi import APIRouter, Request, Depends, HTTPException, status
from fastapi.responses import RedirectResponse
from msal import ConfidentialClientApplication
from urllib.parse import urlencode
from app.config import get_settings
from app.db.mongodb import mongodb
from app.models.user import UserInDB
from jose import jwt
from datetime import datetime, timedelta
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

router = APIRouter(prefix="/auth", tags=["auth"])
settings = get_settings()

# MSAL configuration
CLIENT_ID = settings.MICROSOFT_CLIENT_ID
CLIENT_SECRET = settings.MICROSOFT_CLIENT_SECRET
TENANT_ID = settings.MICROSOFT_TENANT_ID
REDIRECT_URI = settings.MICROSOFT_REDIRECT_URI
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPE = ["User.Read"]

msal_app = ConfidentialClientApplication(
    CLIENT_ID,
    authority=AUTHORITY,
    client_credential=CLIENT_SECRET
)

@router.get("/login")
def login():
    params = {
        "client_id": CLIENT_ID,
        "response_type": "code",
        "redirect_uri": REDIRECT_URI,
        "response_mode": "query",
        "scope": "openid profile email User.Read",
        "state": "12345"
    }
    url = f"{AUTHORITY}/oauth2/v2.0/authorize?{urlencode(params)}"
    return RedirectResponse(url)

@router.get("/callback")
async def auth_callback(request: Request):
    code = request.query_params.get("code")
    logging.info(f"Received auth callback with code: {code}")

    if not code:
        logging.error("Missing code in auth callback.")
        return RedirectResponse(url="/" + "?auth_error=missing_code", status_code=302)
    
    logging.info(f"Attempting to acquire token with scopes: {SCOPE} and redirect_uri: {REDIRECT_URI}")
    
    result = msal_app.acquire_token_by_authorization_code(
        code,
        scopes=SCOPE,
        redirect_uri=REDIRECT_URI
    )
    
    logging.info(f"MSAL acquire_token_by_authorization_code result: {result}")

    if "id_token_claims" not in result:
        logging.error(f"Failed to authenticate with Microsoft SSO: {result}")
        return RedirectResponse(url="/" + "?auth_error=failed_sso", status_code=302)
    claims = result["id_token_claims"]
    email = claims.get("preferred_username")
    name = claims.get("name")
    domain = email.split("@")[-1]
    # Domain restriction
    allowed_domain = os.environ.get("ALLOWED_DOMAIN")
    if allowed_domain and domain != allowed_domain:
        logging.error(f"Unauthorized domain: {domain}")
        return RedirectResponse(url="/" + "?auth_error=unauthorized_domain", status_code=302)
    # Upsert user in DB
    user_doc = await mongodb.get_collection("users").find_one({"email": email})
    now = datetime.utcnow()
    if not user_doc:
        logging.info(f"Creating new user: {email}")
        user_doc = {
            "email": email,
            "full_name": name,
            "is_active": True,
            "is_superuser": False,
            "created_at": now,
            "updated_at": now,
            "last_login": now,
            "roles": ["user"],
            "preferences": {}
        }
        await mongodb.get_collection("users").insert_one(user_doc)
    else:
        logging.info(f"Updating existing user: {email}")
        await mongodb.get_collection("users").update_one(
            {"email": email},
            {"$set": {"last_login": now, "updated_at": now}}
        )
    # Issue JWT
    payload = {
        "sub": email,
        "name": name,
        "roles": user_doc.get("roles", ["user"]),
        "exp": datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    }
    token = jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    logging.info(f"Successfully issued JWT for user: {email}")
    # Redirect to frontend with the access token
    frontend_url = f"http://localhost:5173/?access_token={token}"
    return RedirectResponse(url=frontend_url, status_code=302) 