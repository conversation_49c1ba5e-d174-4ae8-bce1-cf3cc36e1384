from fastapi import APIRouter
from app.api.v1.prompts import router as prompts_router
from app.api.v1.auth import router as auth_router
from app.api.v1.chat import router as chat_router
from app.api.v1.analytics import router as analytics_router
from app.api.v1.websocket import router as websocket_router
from app.api.v1.users import router as users_router

api_router = APIRouter()
api_router.include_router(prompts_router)
api_router.include_router(auth_router)
api_router.include_router(chat_router)
api_router.include_router(analytics_router)
api_router.include_router(websocket_router)
api_router.include_router(users_router) 