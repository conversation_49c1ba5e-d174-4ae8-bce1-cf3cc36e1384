from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Any
from app.core.auth import get_current_user
from app.services.bedrock import bedrock_service
import boto3
from botocore.exceptions import ClientError

router = APIRouter(prefix="/models", tags=["models"])

@router.get("/bedrock", response_model=List[Dict[str, Any]])
async def get_bedrock_models(current_user = Depends(get_current_user)):
    """Get list of available AWS Bedrock models"""
    try:
        # Create a bedrock client to list foundation models
        bedrock_client = boto3.client(
            "bedrock",
            aws_access_key_id=bedrock_service.client._client_config.__dict__.get('_user_provided_options', {}).get('aws_access_key_id'),
            aws_secret_access_key=bedrock_service.client._client_config.__dict__.get('_user_provided_options', {}).get('aws_secret_access_key'),
            region_name=bedrock_service.client._client_config.region_name
        )
        
        # List foundation models
        response = bedrock_client.list_foundation_models()
        
        # Filter and format models for the frontend
        models = []
        for model in response.get('modelSummaries', []):
            # Only include text generation models that are active
            if (model.get('outputModalities') and 'TEXT' in model.get('outputModalities', []) and 
                model.get('modelLifecycle', {}).get('status') == 'ACTIVE'):
                models.append({
                    'id': model.get('modelId'),
                    'name': model.get('modelName'),
                    'provider': model.get('providerName'),
                    'description': f"{model.get('providerName')} - {model.get('modelName')}",
                    'input_modalities': model.get('inputModalities', []),
                    'output_modalities': model.get('outputModalities', [])
                })
        
        # If no models found or API fails, return default models
        if not models:
            models = [
                {
                    'id': 'anthropic.claude-3-sonnet-20240229-v1:0',
                    'name': 'Claude 3 Sonnet',
                    'provider': 'Anthropic',
                    'description': 'Anthropic - Claude 3 Sonnet',
                    'input_modalities': ['TEXT'],
                    'output_modalities': ['TEXT']
                },
                {
                    'id': 'anthropic.claude-3-haiku-20240307-v1:0',
                    'name': 'Claude 3 Haiku',
                    'provider': 'Anthropic',
                    'description': 'Anthropic - Claude 3 Haiku',
                    'input_modalities': ['TEXT'],
                    'output_modalities': ['TEXT']
                },
                {
                    'id': 'amazon.titan-text-express-v1',
                    'name': 'Titan Text Express',
                    'provider': 'Amazon',
                    'description': 'Amazon - Titan Text Express',
                    'input_modalities': ['TEXT'],
                    'output_modalities': ['TEXT']
                },
                {
                    'id': 'ai21.j2-ultra-v1',
                    'name': 'Jurassic-2 Ultra',
                    'provider': 'AI21 Labs',
                    'description': 'AI21 Labs - Jurassic-2 Ultra',
                    'input_modalities': ['TEXT'],
                    'output_modalities': ['TEXT']
                },
                {
                    'id': 'cohere.command-text-v14',
                    'name': 'Command',
                    'provider': 'Cohere',
                    'description': 'Cohere - Command',
                    'input_modalities': ['TEXT'],
                    'output_modalities': ['TEXT']
                }
            ]
        
        return models
        
    except ClientError as e:
        # If AWS credentials are not configured or other AWS errors
        print(f"AWS Bedrock error: {e}")
        # Return default models as fallback
        return [
            {
                'id': 'anthropic.claude-3-sonnet-20240229-v1:0',
                'name': 'Claude 3 Sonnet',
                'provider': 'Anthropic',
                'description': 'Anthropic - Claude 3 Sonnet',
                'input_modalities': ['TEXT'],
                'output_modalities': ['TEXT']
            },
            {
                'id': 'anthropic.claude-3-haiku-20240307-v1:0',
                'name': 'Claude 3 Haiku',
                'provider': 'Anthropic',
                'description': 'Anthropic - Claude 3 Haiku',
                'input_modalities': ['TEXT'],
                'output_modalities': ['TEXT']
            },
            {
                'id': 'amazon.titan-text-express-v1',
                'name': 'Titan Text Express',
                'provider': 'Amazon',
                'description': 'Amazon - Titan Text Express',
                'input_modalities': ['TEXT'],
                'output_modalities': ['TEXT']
            }
        ]
    except Exception as e:
        print(f"Unexpected error fetching Bedrock models: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch available models")
