from datetime import datetime
from typing import List, Optional, Dict
from bson import ObjectId

from app.db.mongodb import get_database
from app.models.prompt import Prompt, PromptCreate, PromptUpdate, PromptInDB, PromptVersion, Project

class PromptService:
    def __init__(self):
        self.db = get_database()
        self.prompts_collection = self.db.prompts
        self.versions_collection = self.db.prompt_versions
        self.projects_collection = self.db.projects

    async def get_list_of_projects(self) -> List[str]:
        """Get list of all project names"""
        projects = await self.projects_collection.find({}, {'name': 1, '_id': 0}).to_list(None)
        return [p['name'] for p in projects]

    async def get_list_of_prompts(self) -> List[PromptInDB]:
        """Get list of all prompts with their metadata"""
        prompts = await self.prompts_collection.find({}).to_list(None)
        return [PromptInDB(**p) for p in prompts]

    async def get_latest_prompt(self, prompt_name: str) -> tuple[Optional[str], Optional[int]]:
        """Get the latest version of a prompt"""
        prompt = await self.prompts_collection.find_one(
            {'title': prompt_name, 'tags': 'latest'},
            sort=[('updated_at', -1)]
        )
        if prompt:
            return prompt['content'], prompt.get('version', 1)
        return None, None

    async def get_prompt_version(self, prompt_name: str, version: int) -> tuple[Optional[str], Optional[List[str]]]:
        """Get a specific version of a prompt"""
        prompt = await self.prompts_collection.find_one({
            'title': prompt_name,
            'version': version
        })
        if prompt:
            return prompt['content'], prompt.get('tags', [])
        return None, None

    async def add_or_update_prompt(self, prompt_name: str, content: str, tags: List[str], model: str = "gpt-4o-mini") -> None:
        """Add or update a prompt with new version"""
        # Get current version
        latest = await self.prompts_collection.find_one(
            {'title': prompt_name},
            sort=[('updated_at', -1)]
        )
        
        # Create new prompt
        prompt_doc = PromptCreate(
            title=prompt_name,
            content=content,
            model=model,
            parameters={},
            tags=tags
        )
        
        # Remove 'latest' tag from previous version
        if latest:
            await self.prompts_collection.update_one(
                {'_id': latest['_id']},
                {'$pull': {'tags': 'latest'}}
            )
        
        # Insert new version
        await self.prompts_collection.insert_one(prompt_doc.dict())

    async def create_project(self, name: str, description: Optional[str] = None) -> str:
        """Create a new project"""
        project = Project(name=name, description=description)
        result = await self.projects_collection.insert_one(project.dict())
        return str(result.inserted_id)

    async def get_prompt_by_id(self, prompt_id: str) -> Optional[PromptInDB]:
        """Get a prompt by its ID"""
        prompt = await self.prompts_collection.find_one({'_id': ObjectId(prompt_id)})
        if prompt:
            return PromptInDB(**prompt)
        return None

    async def update_prompt(self, prompt_id: str, update_data: PromptUpdate) -> Optional[PromptInDB]:
        """Update a prompt"""
        update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
        if update_dict:
            update_dict['updated_at'] = datetime.utcnow()
            result = await self.prompts_collection.update_one(
                {'_id': ObjectId(prompt_id)},
                {'$set': update_dict}
            )
            if result.modified_count:
                return await self.get_prompt_by_id(prompt_id)
        return None

prompt_service = PromptService() 