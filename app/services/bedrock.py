import boto3
from app.config import get_settings

settings = get_settings()

class BedrockService:
    def __init__(self):
        self.client = boto3.client(
            "bedrock-runtime",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION
        )

    def invoke_model(self, model_id: str, prompt: str, system_prompt: str = None, parameters: dict = None):
        body = {
            "prompt": prompt
        }
        if system_prompt:
            body["system_prompt"] = system_prompt
        if parameters:
            body.update(parameters)
        response = self.client.invoke_model(
            modelId=model_id,
            body=body
        )
        return response["completion"]

bedrock_service = BedrockService() 