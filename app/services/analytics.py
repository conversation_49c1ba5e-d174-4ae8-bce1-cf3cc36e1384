from datetime import datetime, timedelta
from typing import Dict, List, Any
from app.db.mongodb import mongodb

class AnalyticsService:
    async def get_user_token_usage(self, user_email: str, days: int = 30) -> Dict[str, Any]:
        start_date = datetime.utcnow() - timedelta(days=days)
        pipeline = [
            {
                "$match": {
                    "user": user_email,
                    "timestamp": {"$gte": start_date}
                }
            },
            {
                "$group": {
                    "_id": "$model_id",
                    "total_tokens": {"$sum": "$token_usage"},
                    "total_requests": {"$sum": 1},
                    "avg_tokens_per_request": {"$avg": "$token_usage"}
                }
            }
        ]
        results = await mongodb.get_collection("chats").aggregate(pipeline).to_list(None)
        return {
            "total_requests": sum(r["total_requests"] for r in results),
            "total_tokens": sum(r["total_tokens"] for r in results),
            "models": results
        }

    async def get_chat_history(
        self,
        user_email: str,
        skip: int = 0,
        limit: int = 50,
        model_id: str = None,
        start_date: datetime = None,
        end_date: datetime = None
    ) -> List[Dict[str, Any]]:
        query = {"user": user_email}
        if model_id:
            query["model_id"] = model_id
        if start_date:
            query["timestamp"] = {"$gte": start_date}
        if end_date:
            query["timestamp"] = {"$lte": end_date}
        
        cursor = mongodb.get_collection("chats").find(
            query,
            sort=[("timestamp", -1)],
            skip=skip,
            limit=limit
        )
        return await cursor.to_list(None)

    async def get_global_analytics(self, days: int = 30) -> Dict[str, Any]:
        start_date = datetime.utcnow() - timedelta(days=days)
        pipeline = [
            {
                "$match": {
                    "timestamp": {"$gte": start_date}
                }
            },
            {
                "$group": {
                    "_id": {
                        "model_id": "$model_id",
                        "date": {"$dateToString": {"format": "%Y-%m-%d", "date": "$timestamp"}}
                    },
                    "total_tokens": {"$sum": "$token_usage"},
                    "total_requests": {"$sum": 1}
                }
            },
            {
                "$group": {
                    "_id": "$_id.model_id",
                    "daily_stats": {
                        "$push": {
                            "date": "$_id.date",
                            "total_tokens": "$total_tokens",
                            "total_requests": "$total_requests"
                        }
                    }
                }
            }
        ]
        results = await mongodb.get_collection("chats").aggregate(pipeline).to_list(None)
        return {"models": results}

analytics_service = AnalyticsService() 