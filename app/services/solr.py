import pysolr
from app.config import get_settings

settings = get_settings()

class SolrService:
    def __init__(self):
        self.client = pysolr.Solr(settings.SOLR_URL, always_commit=True, timeout=10)

    def get_dynamic_fields(self, collection: str):
        # This assumes SOLR schema API is enabled
        response = self.client._send_request('get', f'/solr/{collection}/schema/dynamicfields')
        return response.get('dynamicFields', [])

solr_service = SolrService() 