from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.config import get_settings
from app.api.v1 import api_router
from app.db.mongodb import mongodb
from app.core.error_handlers import setup_exception_handlers
from app.core.rate_limit import rate_limit_middleware

settings = get_settings()

app = FastAPI(
    title=settings.APP_NAME,
    version=settings.VERSION,
    debug=settings.DEBUG
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Frontend development server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup exception handlers
setup_exception_handlers(app)

# Add rate limiting middleware
app.middleware("http")(rate_limit_middleware)

@app.on_event("startup")
async def startup_db_client():
    await mongodb.connect_to_database()

@app.on_event("shutdown")
async def shutdown_db_client():
    await mongodb.close_database_connection()

# Include API router
app.include_router(api_router, prefix="/api/v1")

@app.get("/")
async def root():
    return {
        "message": "Welcome to AI Prompt Playground",
        "version": settings.VERSION
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"} 