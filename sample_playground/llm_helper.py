import pandas as pd
import json
import csv
import re
import tiktoken
import os
import boto3
import litellm
import time
from litellm import completion
from langfuse import Langfuse
from langchain.prompts import PromptTemplate, ChatPromptTemplate, HumanMessagePromptTemplate
from langchain.prompts.chat import ChatPromptTemplate, SystemMessagePromptTemplate

import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning) 

import configparser
config = configparser.ConfigParser()
config.read('configurations.ini')

os.environ["OPENAI_API_KEY"] = config['chatgpt']['api_key']
os.environ["ANTHROPIC_API_KEY"] = config['anthropic']['api_key']
os.environ["AWS_ACCESS_KEY_ID"] = config['aws']['aws_access_key_id']
os.environ["AWS_SECRET_ACCESS_KEY"] = config['aws']['aws_secret_access_key']
os.environ["AWS_REGION_NAME"] = config['aws']['aws_region']

litellm.success_callback = ["langfuse"]

from langfuse.openai import openai
openai.langfuse_enabled = False

def token_limit(text, encoding_name, max_tokens):
    
    """Truncate a string to have `max_tokens` according to the given encoding."""
    
    encoding = tiktoken.get_encoding(encoding_name)
    
    return encoding.decode(encoding.encode(text)[:max_tokens])

def get_preprocess(row, selected_columns):
    try:
        concat_text = ""
        for col in selected_columns:
            if "description" in col:
                concat_text += f"description: " + row[col] + "\n\n"
            else:
                concat_text += f"{col}: " + row[col] + "\n\n"
        
        row['concat_text'] = concat_text
        return row
    
    except Exception as e:
        print("Exception : ", e)
        
        concat_text = None
        return row

def get_result(message, prompt_name, model_name):
  
    tempertaure = 0
    
    if model_name in ["o1-mini", "o1", "o3-mini"]:
        tempertaure = 1
    
    response = completion(
        model=model_name,
        messages=message,
        user="guest",
        metadata={
            "tags": [f"Model: {model_name} Prompt: {prompt_name}"]
        }
            )
    
    prompt_tokens = response.json()['usage']['prompt_tokens']
    completion_tokens = response.json()['usage']['completion_tokens']
    
   
        
    res = {"gpt_response" : response.json()['choices'][0]['message']['content'],
           "prompt_tokens" : prompt_tokens,
           "completion_tokens" : completion_tokens,
          }
    
    return res

def get_langchain_res(row, prompt_query, prompt_name, model_name):

    row['gpt_response'] = None

    encoding_name = "cl100k_base"
    max_tokens = 120000

    try:
        text = token_limit(row['concat_text'], encoding_name, max_tokens)
        message = [
            {"role": "system", "content": prompt_query},
            {"role": "user", "content": text}
        ]
        response = get_result(message=message, prompt_name=prompt_name, model_name=model_name)
        row['gpt_response'] = response.get("gpt_response")
        row['prompt_tokens'] = response.get("prompt_tokens")
        row['completion_tokens'] = response.get("completion_tokens")

    except Exception as e: 
        print("Expception:", e)
        
    return row

def get_postprocess(row):
    
    output_dict = str(row["gpt_response"])
    res_dict = None
    row['is_parsed'] = False
    
    try:
        parsed_dict = json.loads(output_dict)
        row['json_extracted'] = parsed_dict
        json_data = row['json_extracted']
        
    except:
        match_dict = re.findall(r"\{.*\}", output_dict, flags=re.DOTALL)
        if match_dict:
            try: res_dict = eval(match_dict[0])
            except Exception as e: 
                try: res_dict = json.loads(match_dict[0])
                except Exception as e: pass

        row['json_extracted'] = res_dict
        json_data = row['json_extracted']
        
    
    if pd.notna(json_data):
        for key, value in json_data.items():
            row[key] = value
        row['is_parsed'] = True
            
    return row