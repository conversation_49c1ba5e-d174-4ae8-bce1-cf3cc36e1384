import streamlit as st
import pandas as pd
import requests
import numpy as np
import re 
import os
import time
import io
from datetime import datetime

from llm_helper import get_langchain_res, get_preprocess, get_postprocess
from langfuse_helper import add_or_update_prompt, get_latest_prompt, get_latest_prompt_, get_list_of_prompts, get_list_of_projects, add_prompt
from solr_helper import get_pats_text_

buffer = io.BytesIO()

import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning) 

if 'prompt_selected' not in st.session_state:
    st.session_state.prompt_selected = ""

if 'latest_prompt_box' not in st.session_state:
    st.session_state.latest_prompt_box = ""

if 'latest_prompt' not in st.session_state:
    st.session_state.latest_prompt = ""
    
if 'input_box' not in st.session_state:
    st.session_state.input_box = ""

if 'prompts_available' not in st.session_state:
    st.session_state.prompts_available = ["-- Select a prompt --"]
    
if 'selected_version' not in st.session_state:
    st.session_state.selected_version = None
    
if 'gpt_version' not in st.session_state:
    st.session_state.gpt_version = ''

if 'user_prompt' not in st.session_state:
    st.session_state.user_prompt = None
    
if "publish" not in st.session_state:
    st.session_state.publish = False
    
    
def change_prompt_box():
    if st.session_state.latest_prompt_box != st.session_state.prompt_text:
        st.session_state.latest_prompt_box = st.session_state.prompt_text
    print("text_area_updated")
    
def refresh_prompt():
       
    if st.session_state.latest_prompt_box and st.session_state.latest_prompt_box != st.session_state.latest_prompt:
        
        dt_object = datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
        add_or_update_prompt(prompt_name=st.session_state.input_box, 
                             prompt=st.session_state.latest_prompt_box,
                             labels=[dt_object, "latest"])
        
        st.session_state.latest_prompt, version_ = get_latest_prompt_(
                prompt_name=st.session_state.input_box)
        
        _, labels = get_latest_prompt(
            project_selected=st.session_state.input_box, version=version_)
        
        st.session_state.selected_version = f"{version_} - {labels}"        

def change_state():
    if st.session_state.selected_prompt_name != st.session_state.prompt_selected:
        
        if st.session_state.selected_prompt_name != "-- Select a prompt --":
            st.session_state.prompt_selected = st.session_state.selected_prompt_name
        else:
            st.session_state.prompt_selected = ''
            st.session_state.latest_prompt = ''
            
        st.session_state.input_box = st.session_state.prompt_selected
        
def version_change():
    if st.session_state.version!=st.session_state.selected_version:
        st.session_state.selected_version = st.session_state.version
    print(st.session_state.selected_version)
                    
latest_prompt = None
latest_prompt_box = None
user_prompt = None

st.set_page_config(layout="wide")
          
col1, col2 = st.columns([6, 4])

def change_chatbot_style():
    # Set style of chat input so that it shows up at the bottom of the column
    chat_input_style = f"""
    <style>
        .stChatInput {{
          position: fixed;
          bottom: 3rem;
          width: 600px;
        }}
    </style>
    """
    st.markdown(chat_input_style, unsafe_allow_html=True)

with col1:
    
    st.title("💬 Prompt Playground")
    st.write("US20160286537A1")
    change_chatbot_style()

    if "messages" not in st.session_state:
        st.session_state.messages = []

    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    user_prompt = st.chat_input("Provide me patnum :)")
    
    if user_prompt:
        user_prompt = user_prompt.split("\n")
        st.session_state.user_prompt = user_prompt
    
    if st.session_state.user_prompt:
            
        if st.session_state.latest_prompt_box and st.session_state.latest_prompt_box != st.session_state.latest_prompt:
            
            dt_object = datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
            
            if st.session_state.publish==True:
                tags = [f"{dt_object}", "latest", "production"]
                st.session_state.publish = False
            else:
                tags = [f"{dt_object}", "latest"]
            
            add_or_update_prompt(prompt_name=st.session_state.input_box, 
                                 prompt=st.session_state.latest_prompt_box,
                                 labels=tags
            )
            st.session_state.latest_prompt, version_ = get_latest_prompt_(prompt_name=st.session_state.input_box)
            _, labels = get_latest_prompt(project_selected=st.session_state.input_box, version=version_)
            st.session_state.selected_version = f"{version_} - {labels}"
            
                       
        patnum_data = pd.DataFrame(st.session_state.user_prompt, columns=['patnum'])
        data = get_pats_text_(patnum_data)

        st.chat_message("user").markdown(st.session_state.user_prompt)
        
        st.markdown("### Select columns to include:")
        selected_columns = st.multiselect("Choose columns to pass to the model", data.columns)
        
        if st.button("Run", key="run_button", help="Click to process data"):
            
            refresh_prompt()

            # Stop here if no columns selected
            if not selected_columns:
                st.warning("Please select at least one column to continue.")
                st.stop()

            data = data.apply(get_preprocess, selected_columns=selected_columns, axis=1)
            data = data.apply(get_langchain_res,
                              prompt_query=st.session_state.latest_prompt_box,
                              prompt_name=st.session_state.input_box,
                              model_name=st.session_state.gpt_version,
                              axis=1
                             )
            data = data.apply(get_postprocess, axis=1)

            st.session_state.messages.append({"role": "user", "content": st.session_state.user_prompt})

#             response = res['gpt_response']

            with st.chat_message("assistant"):
                st.dataframe(data)

            # Add assistant response to chat history
#             st.session_state.messages.append({"role": "assistant", "content": response})
        
with col2:
   
    st.sidebar.title("Settings")
    
    gpt_version = st.sidebar.selectbox(
        "Select an LLM", [
            "gpt-4o-mini", 
            "gpt-4o",
            "o1-mini",
            "o1",
            "o3-mini",
            "claude-3-7-sonnet-latest",
            "claude-3-5-sonnet-latest",
            "claude-3-5-haiku-latest",
            "meta.llama3-1-8b-instruct-v1:0",
            "meta.llama3-1-70b-instruct-v1:0",
            "meta.llama3-1-405b-instruct-v1:0",
            "meta.llama3-2-1b-instruct-v1:0",
            "meta.llama3-2-3b-instruct-v1:0",
            "meta.llama3-2-11b-instruct-v1:0",
            "meta.llama3-2-90b-instruct-v1:0",
            "meta.llama3-3-70b-instruct-v1:0"
        ])
    
    st.session_state.gpt_version = gpt_version

#     st.link_button(
#         label="Go to langfuse", 
#         url="http://dvpoe-dsa01a:3000/project/cm9b4k3ek00232l8fyxszbmri/prompts"
#     )

    projects = get_list_of_projects()

    project_selected = st.sidebar.selectbox(
        "Select project name", 
        projects,
    )
    
    prompts_meta = get_list_of_prompts()
    name_to_meta = {p.name: p for p in prompts_meta}

    st.session_state.prompts_available = ["-- Select a prompt --"] + list(name_to_meta.keys())
    
    prompt_selected = st.sidebar.selectbox(
        "Select prompt name", 
        st.session_state.prompts_available,
        on_change=change_state,
        key="selected_prompt_name"
    )
        
    if prompt_selected!="-- Select a prompt --":
        selected_meta = name_to_meta[prompt_selected]
        
        versions_list = []

        for i in selected_meta.versions:
            _, labels = get_latest_prompt(project_selected=prompt_selected, version=i)
            label = f"{i} - {labels}"
            versions_list.append(label)
            
        selected_version = st.sidebar.selectbox("Select a Version", versions_list, key="version", on_change=version_change)
        
        if st.session_state.selected_version and st.session_state.selected_version != "-- Select a version --":
            latest_prompt, _ = get_latest_prompt(
                project_selected=prompt_selected, version=int(st.session_state.selected_version[0]))
        
        else:
            latest_prompt, _ = get_latest_prompt(project_selected=prompt_selected, version=int(selected_version[0]))
        st.session_state.latest_prompt = latest_prompt
                
    if st.button("Add New Prompt", key="add_button", help="Click to add new prompt"):
        
        dt_object = datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
            
        if st.session_state.publish==True:
            tags = [f"{dt_object}", "latest", "production"]
            st.session_state.publish = False
        else:
            tags = [f"{dt_object}", "latest"]

        add_prompt(prompt_name=st.session_state.input_box,
                   prompt=st.session_state.latest_prompt_box,
                   labels=tags
        )
        
        st.session_state.prompts_available = st.session_state.prompts_available + [st.session_state.input_box]
        
    input_box = st.text_input("Prompt name", value=st.session_state.prompt_selected)
    st.session_state.input_box = input_box

    latest_prompt_box = st.text_area("System Prompt", height=350, value=st.session_state.latest_prompt, key="prompt_text", on_change=change_prompt_box)
    
    if st.button("Publish prompt to production", key="publish_prompt", help="Click to publish"):
        st.session_state.publish = True
    