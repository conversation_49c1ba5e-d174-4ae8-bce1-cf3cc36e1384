import pandas as pd
import numpy as np
import requests
import html2text
import re
from IPython import embed

def get_pat_wo_kind_code(patnum):
        return re.search("[A-Z]{2}[0-9]+", patnum).group(0)
    
def get_pats_text_(row):
  

    res_dict = pd.DataFrame()

    url = "https://prod-solr-cloud.rpxcorp.com:8080/solr/analyst_pats_search_app_account_0/select"
    params = "?fl=patnum,title,abstract,first_claim_text,claim_texts,descriptions&rows=%s&q=patnum:(%s)"

    username = "insights_app"
    password = "rHy6Xa1x"

    batch = 100

    for i in range(0, row.shape[0], batch):
        URL = url + params % (i+batch,"%20OR%20".join([pat + '*' for pat in row['patnum'][i:i+batch].to_list()]),)
        res = requests.get(URL, auth=(username, password))
        temp = res.json()['response']['docs']

        temp_df = pd.DataFrame(temp)
        res_dict = pd.concat([res_dict, temp_df])

    row = pd.merge(row, res_dict, on="patnum", how='left')

  
    if row.shape[0]==0:
        raise Exception("Patents not found in SOLR")
        
    data = row.copy()
    
        
    for col in ['title', 'abstract', 'first_claim_text', 'claim_texts', 'descriptions']:
        if col in data.columns:
            data[col] = data[col].replace(np.nan, " ", regex=True)
            data[col] = data[col].fillna(" ")
            
    if 'descriptions' in data.columns:
        data["descriptions"] = data["descriptions"].apply(lambda x: x[0])
        data["descriptions_top2K"] = data["descriptions"].apply(lambda x: x[:2000])

    if 'claim_texts' in data.columns: 
        data["claim_texts"] = data["claim_texts"].apply(lambda x: x[0])

    if 'abstract' in data.columns:
        data["abstract"] = data["abstract"].apply(lambda x: x[0])

    if 'first_claim_text' in data.columns:
        data["first_claim_text"] = data["first_claim_text"].apply(lambda x: html2text.html2text(x))
            
    return data