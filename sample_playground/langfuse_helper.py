import os
import sys
import configparser
from langfuse import Langfuse
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
config = configparser.ConfigParser()
config.read('configurations.ini')

# os.environ["LANGFUSE_PUBLIC_KEY"] = config['langfuse_credentials']['langfuse_public_key']
# os.environ["LANGFUSE_SECRET_KEY"] = config['langfuse_credentials']['langfuse_secret_key']
os.environ["LANGFUSE_HOST"] = "http://dvpoe-dsa01a:3000"

import requests
import psycopg2

params = {
    "host": 'dvpoe-dsa01a',
    "user": 'postgres',
    "password": 'postgres',
    "dbname": 'postgres'
}

connector = psycopg2.connect(**params)

def get_list_of_projects():

    response = requests.get(url="http://dvpoe-dsa01a:3000/api/public/projects", 
                            auth=('pk-lf-921750e4-b971-4688-b9b4-aa2a37f11902', '******************************************')
                           )
    return response.json()['data'][0]['name']

def get_list_of_prompts():
    
    os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-921750e4-b971-4688-b9b4-aa2a37f11902"
    os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
    
    lf_obj = Langfuse()
    
    limit = lf_obj.client.prompts.list().pagination['totalItems']
    prompts_list = lf_obj.client.prompts.list(limit=limit).data

    return prompts_list

def get_latest_prompt_(prompt_name):
    
    os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-921750e4-b971-4688-b9b4-aa2a37f11902"
    os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
    
    lf_obj = Langfuse()
    
    try:
        latest_prompt = lf_obj.get_prompt(name=prompt_name, label=['latest'])
        if latest_prompt.prompt:
            return latest_prompt.prompt, latest_prompt.version
    except:
        pass
    
# def get_list_of_prompts():
#     with connector.cursor() as cur:
        
#         cur.execute(f'''select 
#                             distinct(pr.name) as prompt
#                         from 
#                             prompts pr
#                             join projects p on pr.project_id = p.id
#                         where p.name = 'WiFi-6';''')
        
#         results = [i[0] for i in cur.fetchall() if i[0]]
        
#         return results

def get_latest_prompt(project_selected, version):
    os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-921750e4-b971-4688-b9b4-aa2a37f11902"
    os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
    
    lf_obj = Langfuse()
    
    try:
        if project_selected != "-- Select a prompt --":
            latest_prompt = lf_obj.get_prompt(name=project_selected, version=version)
            if latest_prompt.prompt:
                return latest_prompt.prompt, latest_prompt.labels
    except:
        pass
    
def add_or_update_prompt(prompt_name, prompt, labels):
    os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-921750e4-b971-4688-b9b4-aa2a37f11902"
    os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
    
    lf_obj = Langfuse()
    lf_obj.create_prompt(name=prompt_name, prompt=prompt, labels=labels)
    return 0

def add_prompt(prompt_name, prompt, labels):
    os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-921750e4-b971-4688-b9b4-aa2a37f11902"
    os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
    
    lf_obj = Langfuse()
    x = lf_obj.create_prompt(name=prompt_name, prompt=prompt, labels=labels)
    print(x, type(x))
    return 0
