#!/bin/bash

# Function to kill process by port
kill_by_port() {
    local port=$1
    local pid=$(lsof -ti:$port)
    if [ ! -z "$pid" ]; then
        echo "Stopping process on port $port (PID: $pid)"
        kill $pid 2>/dev/null
    fi
}

# Kill backend (port 8000)
kill_by_port 8000

# Kill frontend (port 5173 - default Vite port)
kill_by_port 5173

# Kill any remaining node processes from npm
pkill -f "node.*vite" 2>/dev/null

echo "All services stopped" 