import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from decouple import config
import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.config import Settings
from app.models.user import UserCreate
# from app.db.mongodb import Async<PERSON>ongoDB # Removed incorrect import
from datetime import datetime

async def init_db():
    settings = Settings()
    mongodb_client = AsyncIOMotorClient(settings.MONGODB_URL)
    db = mongodb_client[settings.MONGODB_DB_NAME]

    print("Ensuring 'users' collection exists...")
    try:
        # Ensure the collection is created if it doesn't exist (MongoDB creates on first insert)
        await db.create_collection("users", capped=False) 
        print("'users' collection checked/created.")
    except Exception as e:
        # Ignore CollectionInvalid if it already exists
        if "CollectionAlreadyExists" not in str(e) and "already exists" not in str(e):
            print(f"Could not create collection: {e}")

    # Check for existing superuser
    admin_email = config("DEFAULT_ADMIN_EMAIL", default=None)
    admin_name = config("DEFAULT_ADMIN_NAME", default="Default Admin")

    print(f"DEBUG: Current working directory: {os.getcwd()}")
    print(f"DEBUG: DEFAULT_ADMIN_EMAIL from .env: {admin_email}")

    if admin_email:
        existing_admin = await db["users"].find_one({"email": admin_email})
        if not existing_admin:
            print(f"Creating default superuser: {admin_email}")
            admin_user = UserCreate(
                email=admin_email,
                full_name=admin_name,
                is_active=True,
                is_superuser=True,
                roles=["admin", "user"],
            )
            user_data = admin_user.model_dump()
            user_data["created_at"] = user_data["updated_at"] = datetime.utcnow()
            user_data["last_login"] = None # Initial login will set this
            await db["users"].insert_one(user_data)
            print(f"Default superuser {admin_email} created successfully.")
        else:
            print(f"Superuser {admin_email} already exists.")
    else:
        print("DEFAULT_ADMIN_EMAIL not set in .env. Skipping default admin creation.")

    mongodb_client.close()
    print("Database initialization complete.")

if __name__ == "__main__":
    print("Starting database initialization...")
    asyncio.run(init_db()) 