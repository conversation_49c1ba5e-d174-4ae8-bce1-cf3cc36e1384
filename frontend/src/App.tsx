import React, { useEffect, useState } from "react";
import "./App.css";
import Sidebar from "./components/Sidebar";
import ChatArea from "./components/ChatArea";
import ChatFooter from "./components/ChatFooter";
import Login from "./components/Login";
import { handleAuthCallback } from "./auth";
import { useAppStore } from "./store";
import { connectWebSocket, disconnectWebSocket } from "./ws";
import { jwtDecode } from 'jwt-decode';
import UserManagement from './components/UserManagement';
import Dashboard from './components/Dashboard';
import { addPrompt } from "./api/prompts";

function App() {
  const { user, setUser, setSystemPrompt, setPrompt, systemPrompt, prompt, addPromptToList, llm, setLLM, project, setProject, prompts, setPrompts } = useAppStore();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [authErrorMessage, setAuthErrorMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<'dashboard' | 'user_management' | 'playground'>('dashboard');
  const [newPromptName, setNewPromptName] = useState<string>('');
  const [newSystemPromptContent, setNewSystemPromptContent] = useState<string>('');
  const [isSidebarExpanded, setIsSidebarExpanded] = useState<boolean>(true);

  const toggleSidebar = () => {
    setIsSidebarExpanded(prevState => !prevState);
  };

  const handlePublishPrompt = async () => {
    if (!newPromptName || !newSystemPromptContent) {
      alert('Prompt name and System Prompt content cannot be empty.');
      return;
    }

    try {
      const newPrompt = await addPrompt({
        title: newPromptName,
        content: '', // Assuming content is not used for system prompts here
        model: 'gpt-4o-mini', // Default model, can be made dynamic
        system_prompt: newSystemPromptContent,
        is_template: false,
        is_public: false,
      });
      addPromptToList(newPrompt);
      setNewPromptName('');
      setNewSystemPromptContent('');
      alert('Prompt published successfully!');
    } catch (error) {
      console.error('Error publishing prompt:', error);
      alert('Failed to publish prompt. Please check console for details.');
    }
  };

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const url = new URL(window.location.href);
        const accessTokenParam = url.searchParams.get('access_token');
        const authError = url.searchParams.get('auth_error');

        if (authError) {
          setAuthErrorMessage(authError);
          setIsAuthenticated(false);
          setIsLoading(false);
          return;
        }

        let tokenToProcess: string | null = null;

        if (accessTokenParam) {
          tokenToProcess = accessTokenParam;
        } else {
          const storedToken = localStorage.getItem('access_token');
          if (storedToken) {
            tokenToProcess = storedToken;
          }
        }

        if (tokenToProcess) {
          try {
            const decoded: any = jwtDecode(tokenToProcess);
            if (decoded && typeof decoded === 'object' && 'sub' in decoded) {
              // Construct the user object from the decoded token
              const userData = {
                email: decoded.sub || decoded.preferred_username,
                full_name: decoded.name || decoded.given_name || '',
                is_superuser: decoded.is_superuser || false,
                roles: decoded.roles || ['user'],
              };
              setUser(userData);
              setIsAuthenticated(true);
            } else {
              localStorage.removeItem('access_token');
              setIsAuthenticated(false);
            }
          } catch (error) {
            console.error("Error decoding token:", error);
            localStorage.removeItem('access_token');
            setIsAuthenticated(false);
            setAuthErrorMessage("Invalid session. Please log in again.");
          }
        } else {
          setIsAuthenticated(false);
        }

        // Clean up URL parameters
        const paramsToDelete = ['access_token', 'auth_error'];
        let shouldReplace = false;

        paramsToDelete.forEach(param => {
          if (url.searchParams.has(param)) {
            url.searchParams.delete(param);
            shouldReplace = true;
          }
        });

        if (shouldReplace) {
          window.history.replaceState({}, document.title, url.toString());
        }
        setIsLoading(false);
      } catch (error) {
        console.error('Auth error:', error);
        setIsAuthenticated(false);
        setIsLoading(false);
      }
    };

    // Call handleAuthCallback to process URL parameters and store token if present
    handleAuthCallback();
    checkAuth();
  }, [setUser]);

  useEffect(() => {
    if (user) {
      // Connect to WebSocket using user email or random client ID
      const clientId = user?.email || `guest-${Math.random().toString(36).slice(2, 10)}`;
      connectWebSocket(clientId);
      return () => disconnectWebSocket();
    }
  }, [user]);

  if (isLoading) {
    return <div className="loading-container">Loading authentication status...</div>;
  }

  if (!isAuthenticated) {
    return (
      <div className="App">
        <Login authErrorMessage={authErrorMessage} />
      </div>
    );
  }

  return (
    <div className="app-root light-theme">
      <Sidebar 
        setCurrentPage={setCurrentPage} 
        currentPage={currentPage} 
        currentUser={user} 
        isSidebarExpanded={isSidebarExpanded}
        toggleSidebar={toggleSidebar}
      />
      <main className={`main-content ${isSidebarExpanded ? 'sidebar-expanded' : 'sidebar-collapsed'}`}>
        {currentPage === 'playground' && (
          <div className="playground-layout">
            <div className="playground-settings">
              <div className="sidebar-section">
                <h2>Settings</h2>
                <div className="form-group">
                  <label>Select an LLM</label>
                  <select value={llm} onChange={e => setLLM(e.target.value)}>
                    <option value="gpt-4o-mini">gpt-4o-mini</option>
                    <option value="gpt-4-turbo">gpt-4-turbo</option>
                    <option value="claude-3-sonnet">claude-3-sonnet</option>
                  </select>
                </div>
                <div className="form-group">
                  <label>Select project name</label>
                  <select value={project} onChange={e => setProject(e.target.value)}>
                    <option value="WiFi-6">WiFi-6</option>
                    <option value="Project X">Project X</option>
                  </select>
                </div>
                <div className="form-group">
                  <label>Select prompt name</label>
                  <select value={prompt} onChange={e => setPrompt(e.target.value)}>
                    <option value="">-- Select a prompt --</option>
                    {prompts.map((p) => (
                      <option key={p.id} value={p.id}>{p.title}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            <div className="playground-main-area">
              <div className="playground-header">
                <div className="app-title">
                  <span role="img" aria-label="chat">💬</span>
                  <span>Prompt Playground</span>
                </div>
                <div className="chat-id">US20160286537A1</div>
              </div>
              <ChatArea />
              <ChatFooter />
            </div>
            <div className="prompt-creation-panel">
              <button className="add-new-prompt-btn" onClick={() => {
                setNewPromptName('');
                setNewSystemPromptContent('');
              }}>Add New Prompt</button>
              <div className="form-group">
                <label>Prompt name</label>
                <input
                  type="text"
                  className="prompt-name-input"
                  placeholder=""
                  value={newPromptName}
                  onChange={(e) => setNewPromptName(e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>System Prompt</label>
                <textarea
                  className="system-prompt-input"
                  placeholder=""
                  value={newSystemPromptContent}
                  onChange={(e) => setNewSystemPromptContent(e.target.value)}
                ></textarea>
              </div>
              <button className="publish-prompt-btn" onClick={handlePublishPrompt}>Publish prompt to production</button>
            </div>
          </div>
        )}
        {currentPage === 'dashboard' && (
          <Dashboard />
        )}
        {currentPage === 'user_management' && (
          <UserManagement />
        )}
      </main>
    </div>
  );
}

export default App;
