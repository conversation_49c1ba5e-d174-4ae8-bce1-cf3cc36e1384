import { useAppStore } from './store';

export function login() {
  // Redirect to the backend login endpoint
  window.location.href = `${import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1'}/auth/login`;
}

export function logout() {
  // Clear the token and user data
  localStorage.removeItem('access_token');
  useAppStore.getState().setUser(null);
  // Redirect to home page
  window.location.href = '/';
}

export async function handleAuthCallback(): Promise<string | null | { error: string }> {
  // Check if we have a token or error in the URL (after redirect from Microsoft)
  const params = new URLSearchParams(window.location.search);
  const token = params.get('access_token');
  const authError = params.get('auth_error');
  
  if (token) {
    // Store the token (URL will be cleared by App.tsx)
    localStorage.setItem('access_token', token);
    return token;
  } else if (authError) {
    return { error: authError };
  }
  
  return null;
} 