/* Enhanced Playground Styles */

/* Right-side Display Panel */
.prompt-display-panel {
  width: 380px;
  min-width: 300px;
  max-width: 450px;
  background-color: #ffffff;
  border-left: 1px solid #e0e0e0;
  padding: 20px;
  overflow-y: auto;
  flex-shrink: 0;
}

.prompt-display-panel h3 {
  color: #212529;
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: 600;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 10px;
}

.prompt-display-panel h4 {
  color: #212529;
  margin-bottom: 15px;
  margin-top: 30px;
  font-size: 1.1rem;
  font-weight: 600;
}

.prompt-details {
  margin-bottom: 30px;
}

.prompt-detail-value {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 10px;
  color: #212529;
  font-size: 0.95rem;
  line-height: 1.4;
  min-height: 20px;
}

.system-prompt-display {
  min-height: 80px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.no-prompt-selected {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 40px 20px;
}

.no-prompt-selected p {
  margin: 0;
}

.prompt-creation-section {
  border-top: 1px solid #e0e0e0;
  padding-top: 20px;
}

/* Add Project Button */
.add-project-btn {
  background: none;
  border: 1px solid #ced4da;
  color: #495057;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s, border 0.2s;
  width: 100%;
}

.add-project-btn:hover {
  background: #e9ecef;
  border: 1px solid #b0b3b8;
}

/* Loading States */
.loading-indicator {
  color: #6c757d;
  font-style: italic;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  text-align: center;
}

/* Enhanced Form Groups */
.playground-settings .form-group {
  margin-bottom: 20px;
}

.playground-settings .form-group label {
  display: block;
  font-size: 0.95rem;
  margin-bottom: 6px;
  color: #495057;
  font-weight: 500;
}

.playground-settings .form-group select,
.playground-settings .form-group input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #ced4da;
  background: #ffffff;
  color: #212529;
  font-size: 0.95rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.playground-settings .form-group select:focus,
.playground-settings .form-group input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .prompt-display-panel {
    width: 320px;
    min-width: 280px;
  }
}

@media (max-width: 900px) {
  .playground-layout {
    flex-direction: column;
  }
  
  .playground-settings {
    width: 100%;
    max-width: none;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .prompt-display-panel {
    width: 100%;
    max-width: none;
    border-left: none;
    border-top: 1px solid #e0e0e0;
  }
}

/* Enhanced Publish Button */
.publish-prompt-btn {
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 15px;
  transition: background 0.2s;
  width: 100%;
}

.publish-prompt-btn:hover {
  background: #0056b3;
}

.publish-prompt-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* Enhanced Input Styles */
.prompt-name-input,
.system-prompt-input {
  width: 100%;
  background: #ffffff;
  color: #212529;
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 10px 12px;
  font-size: 0.95rem;
  margin-bottom: 8px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.prompt-name-input:focus,
.system-prompt-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.system-prompt-input {
  min-height: 120px;
  resize: vertical;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.4;
}

/* Error States */
.error-message {
  color: #dc3545;
  font-size: 0.9rem;
  margin-top: 5px;
  padding: 8px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
}

/* Success States */
.success-message {
  color: #155724;
  font-size: 0.9rem;
  margin-top: 5px;
  padding: 8px;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 4px;
}
