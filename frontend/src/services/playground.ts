import axios from 'axios';
import { Prompt, PromptCreate, PromptUpdate, Project, PatentData } from '../types/playground';

const API_URL = '/api/playground';

export const playgroundService = {
    async getProjects(): Promise<string[]> {
        const response = await axios.get(`${API_URL}/projects`);
        return response.data;
    },

    async getPrompts(): Promise<Prompt[]> {
        const response = await axios.get(`${API_URL}/prompts`);
        return response.data;
    },

    async getLatestPrompt(promptName: string): Promise<{ prompt: string; version: number }> {
        const response = await axios.get(`${API_URL}/prompts/${promptName}/latest`);
        return response.data;
    },

    async getPromptVersion(promptName: string, version: number): Promise<{ prompt: string; tags: string[] }> {
        const response = await axios.get(`${API_URL}/prompts/${promptName}/version/${version}`);
        return response.data;
    },

    async createPrompt(prompt: PromptCreate): Promise<void> {
        await axios.post(`${API_URL}/prompts`, prompt);
    },

    async updatePrompt(promptId: string, prompt: PromptUpdate): Promise<Prompt> {
        const response = await axios.put(`${API_URL}/prompts/${promptId}`, prompt);
        return response.data;
    },

    async createProject(project: Project): Promise<{ project_id: string }> {
        const response = await axios.post(`${API_URL}/projects`, project);
        return response.data;
    },

    async getPatents(patnums: string[]): Promise<PatentData[]> {
        const response = await axios.post(`${API_URL}/patents`, patnums);
        return response.data;
    }
}; 