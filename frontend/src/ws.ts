import { useAppStore } from './store';

let ws: WebSocket | null = null;

export function connectWebSocket(clientId: string) {
  const url = (import.meta.env.VITE_WS_URL || 'ws://localhost:8000/api/v1/ws/chat/') + clientId;
  ws = new WebSocket(url);
  useAppStore.getState().setConnectionStatus('CONNECTING');

  ws.onopen = () => {
    useAppStore.getState().setConnectionStatus('CONNECTED');
  };
  ws.onclose = () => {
    useAppStore.getState().setConnectionStatus('DISCONNECTED');
  };
  ws.onerror = () => {
    useAppStore.getState().setConnectionStatus('DISCONNECTED');
  };
  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      if (data.type === 'response' || data.type === 'error') {
        useAppStore.getState().addChatMessage({
          id: Date.now().toString() + '-ai',
          sender: 'ai',
          content: data.content,
          timestamp: new Date().toISOString(),
        });
      }
    } catch {}
  };
}

export function sendWebSocketMessage(message: any) {
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(message));
    return true;
  }
  return false;
}

export function disconnectWebSocket() {
  if (ws) ws.close();
  ws = null;
} 