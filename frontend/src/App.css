body, html, #root, .app-root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
  background: #f8f9fa; /* Changed to light background */
  color: #212529; /* Changed to dark text */
}

.app-root {
  display: flex;
  height: 100vh;
  background: #f8f9fa; /* Changed to light background */
}

.app-root.light-theme {
  background: #f8f9fa; /* Light background for the overall app */
  color: #212529; /* Dark text for light theme */
}

.sidebar {
  width: 250px;
  background-color: #ffffff; /* Changed to white background */
  padding-top: 60px; /* Adjust for fixed main-header */
  flex-shrink: 0;
  border-right: 1px solid #e0e0e0; /* Changed to lighter border */
  height: 100vh; /* Make sidebar fill height */
  overflow-y: auto;
  transition: width 0.3s ease; /* Smooth transition for width */
  position: relative;
}

.sidebar.collapsed {
  width: 60px; /* Collapsed width */
}

.sidebar-toggle-btn {
  position: absolute;
  top: 15px;
  right: 10px;
  background: none;
  border: none;
  color: #6c757d; /* Icon color */
  font-size: 1.2rem;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.sidebar-toggle-btn:hover {
  color: #212529; /* Darker on hover */
}

.sidebar-toggle-btn.collapsed {
  transform: rotate(180deg); /* Rotate arrow when collapsed */
}

.sidebar-header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
}

.sidebar.collapsed .sidebar-header {
  justify-content: center;
}

.sidebar.collapsed .langfuse-version,
.sidebar.collapsed .langfuse-logo {
  display: none;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.sidebar-nav li {
  margin-bottom: 5px;
}

.sidebar-nav .nav-item {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  color: #495057;
  text-decoration: none;
  border-radius: 5px;
  transition: background-color 0.2s;
}

.sidebar.collapsed .sidebar-nav .nav-item {
  justify-content: center;
  padding: 10px;
}

.sidebar-nav .nav-item:hover {
  background-color: #e9ecef;
}

.sidebar-nav .nav-item.active {
  background-color: #007bff;
  color: white;
}

.sidebar-nav .nav-item.active .icon {
  color: white;
}

.sidebar-nav .icon {
  margin-right: 10px;
  font-size: 1.2rem;
  color: #6c757d; /* Default icon color */
}

.sidebar.collapsed .sidebar-nav .icon {
  margin-right: 0;
}

.sidebar-footer {
  border-top: 1px solid #e0e0e0;
  padding: 15px 20px;
  margin-top: auto; /* Pushes footer to the bottom */
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.sidebar.collapsed .sidebar-footer {
  align-items: center;
  padding: 15px 0;
}

.sidebar.collapsed .user-name {
  display: none;
}

.sidebar.collapsed .logout-button .icon {
  margin-right: 0;
}

.logout-button {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.logout-button .icon {
  font-size: 1.2rem;
}

.logout-button:hover {
  background-color: #c82333;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-icon {
  font-size: 1.5rem;
  color: #6c757d;
}

.user-name {
  font-weight: 600;
  color: #212529;
}

.main-header {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background-color: #ffffff; /* White background for header */
  border-bottom: 1px solid #e0e0e0;
  color: #212529;
  gap: 10px;
  position: fixed; /* Make header fixed */
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  box-sizing: border-box; /* Include padding and border in element's total width and height */
}

.main-header .app-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-right: auto; /* Pushes back button to the right */
}

.main-header .back-button {
  background: none;
  border: 1px solid #ced4da;
  color: #495057;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.main-header .back-button:hover {
  background-color: #e9ecef;
}

.main-content.sidebar-expanded {
  padding-top: 60px; /* Add padding for the main-header */
}

.main-content.sidebar-collapsed {
  margin-left: 60px;
  padding-top: 60px; /* Add padding for the main-header */
}

.playground-settings.sidebar-expanded {
  width: 30%;
  min-width: 280px;
  max-width: 350px;
}

.sidebar-section h2 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 24px;
  color: #fff;
}

.app-root.light-theme .sidebar-section h2 {
  color: #212529; /* Dark text for titles in light theme */
}

.form-group {
  margin-bottom: 24px;
}
.form-group label {
  display: block;
  font-size: 0.98rem;
  margin-bottom: 8px;
  color: #495057; /* Changed to dark text */
}
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #ced4da; /* Changed to light border */
  background: #e9ecef; /* Changed to light background */
  color: #212529; /* Changed to dark text */
  font-size: 1rem;
}

.app-root.light-theme .form-group select {
  background-color: #e9ecef; /* Light gray background for selects */
  color: #212529; /* Dark text */
  border: 1px solid #ced4da; /* Light border */
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ffffff; /* Changed to white background */
  padding: 0 0 0 0;
}

.app-root.light-theme .main-content {
  background-color: #ffffff; /* White background for main content in light theme */
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 40px 0 40px;
}
.app-title {
  font-size: 2.2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 16px;
  color: #212529; /* Changed to dark text */
}
.connection-status {
  color: #495057; /* Changed to dark text */
  font-size: 1rem;
  letter-spacing: 1px;
  font-weight: 500;
}

.app-root.light-theme .app-title,
.app-root.light-theme .connection-status {
  color: #212529; /* Dark text for titles and connection status in light theme */
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 24px 0 0 0;
}
.chat-id {
  font-size: 1.1rem;
  color: #495057; /* Changed to dark text */
  margin-bottom: 16px;
}

.app-root.light-theme .chat-id {
  color: #212529; /* Dark text for chat ID in light theme */
}

.prompt-panel {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding: 24px 40px 0 40px;
  gap: 12px;
}
.add-prompt-btn {
  background: none;
  border: 1px solid #ced4da; /* Changed to light border */
  color: #495057; /* Changed to dark text */
  padding: 8px 18px;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  margin-bottom: 8px;
  transition: background 0.2s, border 0.2s;
}
.add-prompt-btn:hover {
  background: #e9ecef; /* Changed to light background */
  border: 1px solid #b0b3b8; /* Changed to lighter border */
}
.prompt-name-input, .system-prompt-input {
  width: 100%;
  background: #e9ecef; /* Changed to light background */
  color: #212529; /* Changed to dark text */
  border: 1px solid #ced4da; /* Added light border */
  border-radius: 8px;
  padding: 10px 14px;
  font-size: 1rem;
  margin-bottom: 8px;
}
.system-prompt-input {
  min-height: 100px;
  resize: vertical;
}
.publish-btn {
  background: #007bff; /* Changed to blue */
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 8px;
  transition: background 0.2s;
}
.publish-btn:hover {
  background: #0056b3; /* Darker blue on hover */
}

.chat-footer {
  display: flex;
  align-items: center;
  padding: 24px 40px;
  background: transparent;
  border-top: 1px solid #e0e0e0; /* Changed to lighter border */
}
.chat-input {
  flex: 1;
  background: #e9ecef; /* Changed to light background */
  color: #212529; /* Changed to dark text */
  border: 1px solid #ced4da; /* Added light border */
  border-radius: 8px;
  padding: 12px 18px;
  font-size: 1.1rem;
  margin-right: 12px;
}
.send-btn {
  background: #007bff; /* Changed to blue */
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  padding: 10px 19px;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.send-btn:hover {
  background: #0056b3; /* Darker blue on hover */
}

.app-root.light-theme .chat-footer {
  border-top: 1px solid #e0e0e0; /* Lighter border for chat footer */
}

.app-root.light-theme .send-btn {
  background-color: #007bff; /* Blue for send button */
}

.app-root.light-theme .send-btn:hover {
  background-color: #0056b3; /* Darker blue on hover */
}

@media (max-width: 900px) {
  .app-root {
    flex-direction: column;
  }
  .sidebar {
    width: 100%;
    min-width: unset;
    border-radius: 0;
    padding: 20px 10px;
  }
  .main-content {
    border-radius: 0;
    padding: 0;
  }
  .app-header, .prompt-panel, .chat-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
}

/* Login Page Styles */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f5f5f5;
}

.login-box {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.login-box h1 {
  margin-bottom: 1rem;
  color: #333;
}

.login-box p {
  margin-bottom: 2rem;
  color: #666;
}

.auth-error-message {
  color: #d32f2f;
  margin-bottom: 1rem;
  font-weight: bold;
}

.microsoft-login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: #007bff; /* Changed to blue */
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.microsoft-login-btn:hover {
  background-color: #0056b3; /* Darker blue on hover */
}

.microsoft-logo {
  height: 24px;
  width: auto;
}

.App {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa; /* Changed to light background */
  color: #212529; /* Changed to dark text */
}

.app-root {
  display: flex;
  flex: 1;
}

.navbar {
  display: none; /* Hide the navbar */
}

.app-root.light-theme .navbar {
  background-color: #ffffff; /* White background for navbar in light theme */
  border-bottom: 1px solid #e0e0e0; /* Lighter border */
  color: #212529; /* Dark text for light theme */
}

.navbar-left .app-title {
  margin: 0;
  font-size: 1.5em;
  color: #007bff; /* Blue for app title in light theme */
}

.app-root.light-theme .navbar-left .app-title {
  color: #007bff; /* Blue for app title in light theme */
}

.navbar-right {
  display: none; /* Hide navbar right content */
}

.navbar-button {
  display: none; /* Hide navbar buttons */
}

.app-root.light-theme .navbar-button {
  background-color: #e9ecef; /* Light gray for buttons in light theme */
  color: #212529; /* Dark text */
}

.navbar-button:hover {
  background-color: #dee2e6;
}

.app-root.light-theme .navbar-button:hover {
  background-color: #dee2e6; /* Lighter hover for buttons */
}

.navbar-button.active {
  background-color: #007bff;
  color: #ffffff;
  font-weight: bold;
}

.app-root.light-theme .navbar-button.active {
  background-color: #007bff; /* Blue for active button */
  color: #ffffff; /* White text */
}

.user-profile {
  display: none; /* Hide user profile from navbar */
}

.app-root.light-theme .user-name {
  color: #212529; /* Dark text for user name */
}

.profile-icon {
  display: none; /* Hide profile icon from navbar */
}

.logout-button {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.app-root.light-theme .logout-button {
  background-color: #dc3545; /* Red for logout button (can remain the same) */
  color: white;
}

.dashboard-page-container, .user-management-container {
  padding: 20px;
  background-color: #f1f3f5; /* Changed to light background */
  border-radius: 8px;
  margin-top: 20px;
  margin-left: 20px;
  margin-right: 20px;
  flex: 1;
}

.app-root.light-theme .dashboard-page-container, .app-root.light-theme .user-management-container {
  background-color: #f1f3f5; /* Lighter background for dashboard/user management */
  color: #212529; /* Dark text */
}

/* Playground Layout */
.playground-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.playground-main-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ffffff; /* Changed to white background */
}

.playground-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 32px 0 0 0;
  margin-bottom: 24px;
}

.app-root.light-theme .playground-header {
  color: #212529; /* Dark text for playground header */
}

.prompt-creation-panel {
  width: 380px;
  min-width: 300px;
  max-width: 450px;
  background: #ffffff; /* Changed to white background */
  padding: 24px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex-shrink: 0;
  margin-top: 0; /* Adjust for fixed navbar */
}

.app-root.light-theme .prompt-creation-panel {
  background-color: #ffffff; /* White background for prompt creation panel */
  border: 1px solid #e0e0e0; /* Light border */
}

.add-new-prompt-btn {
  background: #007bff; /* Changed to blue */
  border: none; /* Changed to no border */
  color: #fff;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s, border 0.2s;
  width: 100%;
}

.app-root.light-theme .add-new-prompt-btn {
  background-color: #007bff; /* Blue background for add new prompt button */
  color: #ffffff; /* White text */
  border: none;
}

.add-new-prompt-btn:hover {
  background: #0056b3; /* Darker blue on hover */
  border: none; /* Changed to no border */
}

.app-root.light-theme .add-new-prompt-btn:hover {
  background-color: #0056b3; /* Darker blue on hover */
}

.publish-prompt-btn {
  background: #28a745; /* Changed to green */
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  width: 100%;
}

.app-root.light-theme .publish-prompt-btn {
  background-color: #28a745; /* Green background for publish button */
  color: #ffffff; /* White text */
}

.publish-prompt-btn:hover {
  background: #218838; /* Darker green on hover */
}

.app-root.light-theme .publish-prompt-btn:hover {
  background-color: #218838; /* Darker green on hover */
}

.app-root.light-theme .app-title,
.app-root.light-theme .chat-id,
.app-root.light-theme .connection-status,
.app-root.light-theme .sidebar-section h2,
.app-root.light-theme .form-group label {
  color: #212529; /* Dark text for titles and labels in light theme */
}

.prompt-name-input, .system-prompt-input {
  width: 100%;
  background: #e9ecef; /* Changed to light background */
  color: #212529; /* Changed to dark text */
  border: 1px solid #ced4da; /* Added light border */
  border-radius: 8px;
  padding: 10px 14px;
  font-size: 1rem;
  margin-bottom: 8px; /* Ensures spacing between inputs */
}

.app-root.light-theme .prompt-name-input,
.app-root.light-theme .system-prompt-input,
.app-root.light-theme .form-group select,
.app-root.light-theme .chat-input {
  background-color: #e9ecef; /* Light gray background for inputs and selects */
  color: #212529; /* Dark text */
  border: 1px solid #ced4da; /* Light border */
}

.app-root.light-theme .chat-footer {
  border-top: 1px solid #e0e0e0; /* Lighter border for chat footer */
}

.app-root.light-theme .send-btn {
  background-color: #007bff; /* Blue for send button */
}

.app-root.light-theme .send-btn:hover {
  background-color: #0056b3; /* Darker blue on hover */
}

/* Remove styles from prompt-panel that are now handled in App.tsx */
.prompt-panel {
  display: none; /* Hide the original prompt panel */
}

/* Remove styles from app-header that are now handled in Navbar and playground-header */
.app-header {
  display: none; /* Hide the original app header */
}

.prompt-name-input, .system-prompt-input {
  width: 100%;
  background: #e9ecef; /* Changed to light background */
  color: #212529; /* Changed to dark text */
  border: 1px solid #ced4da; /* Added light border */
  border-radius: 8px;
  padding: 10px 14px;
  font-size: 1rem;
  margin-bottom: 8px; /* Ensures spacing between inputs */
}

.system-prompt-input {
  min-height: 150px; /* Increased height for better visibility */
  resize: vertical;
}

/* Adjustments for main-content and sidebar padding due to fixed navbar */
.sidebar {
  width: 250px;
  background-color: #ffffff; /* Changed to white background */
  padding-top: 60px; /* Adjust for fixed main-header */
  flex-shrink: 0;
  border-right: 1px solid #e0e0e0; /* Changed to light border */
  height: 100vh; /* Make sidebar fill height */
  overflow-y: auto;
}

.main-content {
  flex-grow: 1;
  padding: 0; /* Removed padding here as it's now handled by children */
  padding-top: 60px; /* Add padding for the main-header */
  overflow-y: auto;
}

/* Existing dashboard and user management styles */
.dashboard-page-container, .user-management-container {
  padding: 20px;
  background-color: #f1f3f5; /* Changed to light background */
  border-radius: 8px;
  margin-top: 20px;
  margin-left: 20px;
  margin-right: 20px;
  flex: 1;
}

/* New Dashboard Styles (Langfuse inspired) */
.dashboard-container {
  padding: 20px 40px;
  background-color: #f8f9fa; /* Light background */
  color: #212529; /* Dark text */
  flex: 1;
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-header h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
  color: #212529;
}

.new-prompt-btn {
  background-color: #007bff; /* Blue button */
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.new-prompt-btn:hover {
  background-color: #0056b3;
}

.dashboard-controls {
  margin-bottom: 20px;
}

.filters-btn {
  background-color: #e9ecef; /* Light gray button */
  color: #212529;
  border: 1px solid #ced4da;
  padding: 8px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.filters-btn:hover {
  background-color: #dee2e6;
}

.dropdown-arrow {
  margin-left: 5px;
}

.prompts-table-container {
  flex: 1;
  overflow-x: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #ffffff;
}

.prompts-table {
  width: 100%;
  border-collapse: collapse;
}

.prompts-table th,
.prompts-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  color: #212529;
}

.prompts-table th {
  background-color: #f1f3f5;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
}

.prompts-table tbody tr:last-child td {
  border-bottom: none;
}

.prompts-table tbody tr:hover {
  background-color: #f8f9fa;
}

.pagination-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 15px 0;
  font-size: 0.9rem;
  color: #212529;
}

.pagination-controls span {
  margin-right: 10px;
}

.pagination-controls select {
  padding: 5px 8px;
  border-radius: 5px;
  border: 1px solid #ced4da;
  background-color: #ffffff;
  margin-right: 10px;
  color: #212529;
}

.pagination-controls button {
  background-color: #e9ecef;
  color: #212529;
  border: 1px solid #ced4da;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  margin-left: 5px;
  transition: background-color 0.2s;
}

.pagination-controls button:hover {
  background-color: #dee2e6;
}

/* Specific light theme adjustments for sidebar navigation */
.app-root.light-theme .sidebar-header {
  background-color: #ffffff;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 10px;
  min-height: 1.6em;
}

.app-root.light-theme .langfuse-logo {
  height: 30px; /* Adjust as needed */
  width: auto;
}

.app-root.light-theme .langfuse-version {
  font-size: 0.8rem;
  color: #6c757d;
}

.app-root.light-theme .sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.app-root.light-theme .sidebar-nav li {
  margin-bottom: 5px;
}

.app-root.light-theme .sidebar-nav .nav-item {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  color: #495057;
  text-decoration: none;
  border-radius: 5px;
  transition: background-color 0.2s;
}

.app-root.light-theme .sidebar-nav .nav-item:hover {
  background-color: #e9ecef;
}

.app-root.light-theme .sidebar-nav .nav-item.active {
  background-color: #007bff;
  color: white;
}

.app-root.light-theme .sidebar-nav .nav-item.active .icon {
  color: white;
}

.app-root.light-theme .sidebar-nav .icon {
  margin-right: 10px;
  font-size: 1.2rem;
  color: #6c757d; /* Default icon color */
}

.app-root.light-theme .sidebar-footer {
  border-top: 1px solid #e0e0e0;
  padding: 15px 20px;
  margin-top: auto; /* Pushes footer to the bottom */
}

.app-root.light-theme .user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-root.light-theme .user-icon {
  font-size: 1.5rem;
  color: #6c757d;
}

.app-root.light-theme .user-name {
  font-weight: 600;
  color: #212529;
}

/* Playground specific settings styles */
.app-root.light-theme .playground-settings {
  background-color: #ffffff; /* White background for settings panel */
  padding: 20px;
  border-right: 1px solid #e0e0e0;
  min-width: 280px;
  max-width: 350px;
  width: 30%;
  flex-shrink: 0;
  overflow-y: auto;
}

.app-root.light-theme .playground-settings h2 {
  color: #212529;
  margin-bottom: 20px;
}

.app-root.light-theme .playground-settings .form-group label {
  color: #495057;
}

.app-root.light-theme .playground-settings .form-group select,
.app-root.light-theme .playground-settings .prompt-name-input,
.app-root.light-theme .playground-settings .system-prompt-input {
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  color: #212529;
}

.user-management-container {
  padding: 20px 40px;
  background-color: #f8f9fa;
  color: #212529;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.user-management-header h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
  color: #212529;
}

.add-user-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.add-user-btn:hover {
  background-color: #0056b3;
}

.user-management-table-container {
  flex: 1;
  overflow-x: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #ffffff;
}

.user-management-table {
  width: 100%;
  border-collapse: collapse;
}

.user-management-table th,
.user-management-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  color: #212529;
}

.user-management-table th {
  background-color: #f1f3f5;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
}

.user-management-table tbody tr:last-child td {
  border-bottom: none;
}

.user-management-table tbody tr:hover {
  background-color: #f8f9fa;
}

.user-management-table input[type="email"],
.user-management-table input[type="text"],
.user-management-table select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: #e9ecef;
  color: #212529;
}

.user-management-table input[type="checkbox"] {
  margin-left: 0;
}

.user-management-table button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  margin-right: 5px;
  transition: background-color 0.2s;
}

.user-management-table button:hover {
  background-color: #0056b3;
}

.user-management-table button:last-child {
  margin-right: 0;
}

.user-management-table button.delete-btn {
  background-color: #dc3545;
}

.user-management-table button.delete-btn:hover {
  background-color: #c82333;
}

.user-management-table button.cancel-btn {
  background-color: #6c757d;
}

.user-management-table button.cancel-btn:hover {
  background-color: #5a6268;
}

.prompts-table .action-btn {
  background-color: #e9ecef;
  color: #212529;
  border: 1px solid #ced4da;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  margin-right: 5px;
  transition: background-color 0.2s;
}

.prompts-table .action-btn:hover {
  background-color: #dee2e6;
}

.prompts-table .action-btn:last-child {
  margin-right: 0;
}
