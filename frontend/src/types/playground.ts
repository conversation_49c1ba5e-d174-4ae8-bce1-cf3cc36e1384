export interface Prompt {
    id: string;
    title: string;
    content: string;
    model: string;
    system_prompt?: string;
    parameters: Record<string, any>;
    is_template: boolean;
    is_public: boolean;
    created_by?: string;
    created_at: string;
    updated_at: string;
    usage_count: number;
    average_tokens?: number;
    tags: string[];
}

export interface PromptCreate {
    title: string;
    content: string;
    model: string;
    system_prompt?: string;
    parameters?: Record<string, any>;
    is_template?: boolean;
    is_public?: boolean;
    tags?: string[];
}

export interface PromptUpdate {
    title?: string;
    content?: string;
    model?: string;
    system_prompt?: string;
    parameters?: Record<string, any>;
    is_template?: boolean;
    is_public?: boolean;
    tags?: string[];
}

export interface Project {
    name: string;
    description?: string;
    created_at: string;
    updated_at: string;
}

export interface PatentData {
    patnum: string;
    title?: string;
    abstract?: string;
    first_claim_text?: string;
    claim_texts?: string[];
    descriptions?: string[];
}

export interface PlaygroundState {
    selectedPrompt: string;
    selectedVersion: string;
    latestPrompt: string;
    latestPromptBox: string;
    inputBox: string;
    promptsAvailable: string[];
    gptVersion: string;
    userPrompt: string[];
    publish: boolean;
} 