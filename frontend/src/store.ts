import { create } from 'zustand';
import type { Prompt } from './api/prompts';
import type { LLMModel } from './api/models';

export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai';
  content: string;
  timestamp: string;
}

interface AppState {
  // Existing state
  llm: string;
  project: string;
  prompt: string;
  systemPrompt: string;
  chatMessages: ChatMessage[];
  connectionStatus: 'CONNECTING' | 'CONNECTED' | 'DISCONNECTED';
  user: null | { email: string; full_name: string; is_superuser: boolean; roles: string[] };
  prompts: Prompt[];
  analytics: any;
  chatHistory: any[];
  
  // New state for enhanced playground
  availableModels: LLMModel[];
  availableProjects: string[];
  selectedPromptDetails: Prompt | null;
  isLoadingModels: boolean;
  isLoadingProjects: boolean;
  isLoadingPrompts: boolean;
  
  // Existing actions
  setLLM: (llm: string) => void;
  setProject: (project: string) => void;
  setPrompt: (prompt: string) => void;
  setSystemPrompt: (systemPrompt: string) => void;
  addChatMessage: (msg: ChatMessage) => void;
  setConnectionStatus: (status: AppState['connectionStatus']) => void;
  setUser: (user: AppState['user']) => void;
  clearChat: () => void;
  setPrompts: (prompts: Prompt[]) => void;
  addPromptToList: (prompt: Prompt) => void;
  setAnalytics: (analytics: any) => void;
  setChatHistory: (history: any[]) => void;
  
  // New actions for enhanced playground
  setAvailableModels: (models: LLMModel[]) => void;
  setAvailableProjects: (projects: string[]) => void;
  setSelectedPromptDetails: (prompt: Prompt | null) => void;
  setIsLoadingModels: (loading: boolean) => void;
  setIsLoadingProjects: (loading: boolean) => void;
  setIsLoadingPrompts: (loading: boolean) => void;
}

export const useAppStore = create<AppState>((set) => ({
  // Existing state
  llm: 'anthropic.claude-3-sonnet-20240229-v1:0',
  project: '',
  prompt: '',
  systemPrompt: '',
  chatMessages: [],
  connectionStatus: 'CONNECTING',
  user: null,
  prompts: [],
  analytics: null,
  chatHistory: [],
  
  // New state for enhanced playground
  availableModels: [],
  availableProjects: [],
  selectedPromptDetails: null,
  isLoadingModels: false,
  isLoadingProjects: false,
  isLoadingPrompts: false,
  
  // Existing actions
  setLLM: (llm) => set({ llm }),
  setProject: (project) => set({ project }),
  setPrompt: (prompt) => set({ prompt }),
  setSystemPrompt: (systemPrompt) => set({ systemPrompt }),
  addChatMessage: (msg) => set((state) => ({ chatMessages: [...state.chatMessages, msg] })),
  setConnectionStatus: (status) => set({ connectionStatus: status }),
  setUser: (user) => set({ user }),
  clearChat: () => set({ chatMessages: [] }),
  setPrompts: (prompts) => set({ prompts }),
  addPromptToList: (prompt) => set((state) => ({ prompts: [...state.prompts, prompt] })),
  setAnalytics: (analytics) => set({ analytics }),
  setChatHistory: (history) => set({ chatHistory: history }),
  
  // New actions for enhanced playground
  setAvailableModels: (models) => set({ availableModels: models }),
  setAvailableProjects: (projects) => set({ availableProjects: projects }),
  setSelectedPromptDetails: (prompt) => set({ selectedPromptDetails: prompt }),
  setIsLoadingModels: (loading) => set({ isLoadingModels: loading }),
  setIsLoadingProjects: (loading) => set({ isLoadingProjects: loading }),
  setIsLoadingPrompts: (loading) => set({ isLoadingPrompts: loading }),
}));