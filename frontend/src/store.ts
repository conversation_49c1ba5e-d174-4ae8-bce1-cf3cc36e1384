import { create } from 'zustand';
import type { Prompt } from './api/prompts';

export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai';
  content: string;
  timestamp: string;
}

interface AppState {
  llm: string;
  project: string;
  prompt: string;
  systemPrompt: string;
  chatMessages: ChatMessage[];
  connectionStatus: 'CONNECTING' | 'CONNECTED' | 'DISCONNECTED';
  user: null | { email: string; full_name: string; is_superuser: boolean; roles: string[] };
  prompts: Prompt[];
  analytics: any;
  chatHistory: any[];
  setLLM: (llm: string) => void;
  setProject: (project: string) => void;
  setPrompt: (prompt: string) => void;
  setSystemPrompt: (systemPrompt: string) => void;
  addChatMessage: (msg: ChatMessage) => void;
  setConnectionStatus: (status: AppState['connectionStatus']) => void;
  setUser: (user: AppState['user']) => void;
  clearChat: () => void;
  setPrompts: (prompts: Prompt[]) => void;
  addPromptToList: (prompt: Prompt) => void;
  setAnalytics: (analytics: any) => void;
  setChatHistory: (history: any[]) => void;
}

export const useAppStore = create<AppState>((set) => ({
  llm: 'gpt-4o-mini',
  project: 'WiFi-6',
  prompt: '',
  systemPrompt: '',
  chatMessages: [],
  connectionStatus: 'CONNECTING',
  user: null,
  prompts: [],
  analytics: null,
  chatHistory: [],
  setLLM: (llm) => set({ llm }),
  setProject: (project) => set({ project }),
  setPrompt: (prompt) => set({ prompt }),
  setSystemPrompt: (systemPrompt) => set({ systemPrompt }),
  addChatMessage: (msg) => set((state) => ({ chatMessages: [...state.chatMessages, msg] })),
  setConnectionStatus: (status) => set({ connectionStatus: status }),
  setUser: (user) => set({ user }),
  clearChat: () => set({ chatMessages: [] }),
  setPrompts: (prompts) => set({ prompts }),
  addPromptToList: (prompt) => set((state) => ({ prompts: [...state.prompts, prompt] })),
  setAnalytics: (analytics) => set({ analytics }),
  setChatHistory: (history) => set({ chatHistory: history }),
})); 