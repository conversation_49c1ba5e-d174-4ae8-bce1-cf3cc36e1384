// Type definitions for the application
export interface User {
  id: string;
  email: string;
  full_name: string;
  is_active: boolean;
  is_superuser: boolean;
  roles: string[];
  created_at?: string; // Optional, as it might be added by the backend
  updated_at?: string; // Optional, as it might be added by the backend
}

export interface Prompt {
  id: string;
  title: string;
  content: string;
  model: string;
  system_prompt: string;
  is_template: boolean;
  is_public: boolean;
}

// Ensure this file is treated as a module
export {}; 