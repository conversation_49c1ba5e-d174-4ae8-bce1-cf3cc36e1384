import React, { useEffect, useRef } from "react";
import { useAppStore } from "../store";

const ChatArea: React.FC = () => {
  const chatMessages = useAppStore((s) => s.chatMessages);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatMessages]);

  return (
    <div className="chat-area">
      <div className="messages-container">
        {chatMessages.map((message) => (
          <div key={message.id} className={`chat-message ${message.sender}`}>
            {message.content}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default ChatArea;