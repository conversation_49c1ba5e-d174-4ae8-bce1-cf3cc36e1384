import React from "react";
import { useAppStore } from "../store";
import { logout } from "../auth";

interface SidebarProps {
  setCurrentPage: (page: 'dashboard' | 'user_management' | 'playground') => void;
  currentPage: 'dashboard' | 'user_management' | 'playground';
  currentUser: { is_superuser?: boolean; full_name?: string; email?: string } | null;
  isSidebarExpanded: boolean;
  toggleSidebar: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ setCurrentPage, currentPage, currentUser, isSidebarExpanded, toggleSidebar }) => {
  const { user } = useAppStore();

  return (
    <aside className={`sidebar ${isSidebarExpanded ? 'expanded' : 'collapsed'}`}>
      <div className="sidebar-header">
        <img src="/langfuse-logo.svg" alt="Langfuse Logo" className="langfuse-logo" />
        <span className="langfuse-version">V3.52.0 OSS</span>
        <button onClick={toggleSidebar} className="sidebar-toggle-btn">
          {isSidebarExpanded ? '❮' : '❯'}
        </button>
      </div>
      <nav className="sidebar-nav">
        <ul>
          <li>
            <a href="#" className={`nav-item ${currentPage === 'dashboard' ? 'active' : ''}`} onClick={() => setCurrentPage('dashboard')}>
              <span className="icon">📊</span> {isSidebarExpanded && 'Dashboards'}
            </a>
          </li>
          <li>
            <a href="#" className="nav-item">
              <span className="icon">📈</span> {isSidebarExpanded && 'Tracing'}
            </a>
          </li>
          <li>
            <a href="#" className={`nav-item ${currentPage === 'user_management' ? 'active' : ''}`} onClick={() => setCurrentPage('user_management')}>
              <span className="icon">👤</span> {isSidebarExpanded && 'Users'}
            </a>
          </li>
          <li>
            <a href="#" className="nav-item">
              <span className="icon">🗄️</span> {isSidebarExpanded && 'Datasets'}
            </a>
          </li>
          <li>
            <a href="#" className={`nav-item ${currentPage === 'playground' ? 'active' : ''}`} onClick={() => setCurrentPage('playground')}>
              <span className="icon">✨</span> {isSidebarExpanded && 'Playground'}
            </a>
          </li>
          {currentUser?.is_superuser && (
            <li>
              <a href="#" className={`nav-item ${currentPage === 'user_management' ? 'active' : ''}`} onClick={() => setCurrentPage('user_management')}>
                <span className="icon">👥</span> {isSidebarExpanded && 'User Management'}
              </a>
            </li>
          )}
        </ul>
      </nav>
      <div className="sidebar-footer">
        <button onClick={logout} className="logout-button">
          <span className="icon">➡️</span> {isSidebarExpanded && 'Logout'}
        </button>
        <div className="user-info">
          <span className="user-icon">👤</span>
          {isSidebarExpanded && <span className="user-name">{user?.full_name || user?.email || 'Guest'}</span>}
        </div>
      </div>
    </aside>
  );
};

export default Sidebar; 