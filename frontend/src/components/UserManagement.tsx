import React, { useEffect, useState } from 'react';

// Define User interface locally
interface User {
  id: string;
  email: string;
  full_name: string;
  is_active: boolean;
  is_superuser: boolean;
  roles: string[];
  created_at?: string;
  updated_at?: string;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [editingUserId, setEditingUserId] = useState<string | null>(null);
  const [newUser, setNewUser] = useState<Partial<User> | null>(null);

  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error("No access token found");
        return;
      }
      const response = await fetch('http://localhost:8000/api/v1/users/', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data: User[] = await response.json();
      setUsers(data);
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleEdit = (user: User) => {
    setEditingUserId(user.id);
  };

  const handleSave = async (userToSave: User) => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error("No access token found");
        return;
      }

      const method = userToSave.id === "new" ? 'POST' : 'PUT';
      const url = userToSave.id === "new" ? 'http://localhost:8000/api/v1/users/' : `http://localhost:8000/api/v1/users/${userToSave.id}`;
      
      console.log('Saving user:', userToSave); // Log the user data being sent
      
      const response = await fetch(url, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(userToSave)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        console.error('Server response:', {
          status: response.status,
          statusText: response.statusText,
          errorData
        });
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData?.detail || response.statusText}`);
      }

      await fetchUsers(); // Re-fetch users to get updated list
      setEditingUserId(null);
      setNewUser(null);
    } catch (error) {
      console.error("Error saving user:", error);
      alert(`Failed to save user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleCancel = () => {
    setEditingUserId(null);
    setNewUser(null);
  };

  const handleDelete = async (userId: string) => {
    if (!window.confirm("Are you sure you want to delete this user?")) {
      return;
    }
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error("No access token found");
        return;
      }
      const response = await fetch(`http://localhost:8000/api/v1/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      await fetchUsers(); // Re-fetch users
    } catch (error) {
      console.error("Error deleting user:", error);
      alert("Failed to delete user. Check console for details.");
    }
  };

  const handleAddUser = () => {
    setNewUser({
      id: "new", // Temporary ID for new user
      email: "",
      full_name: "",
      is_active: true,
      is_superuser: false,
      roles: ["user"] // Default role
    });
    setEditingUserId("new");
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>, field: keyof User, userId: string) => {
    const value = e.target.type === 'checkbox' ? (e.target as HTMLInputElement).checked : e.target.value;
    
    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === userId ? { ...user, [field]: value } : user
      )
    );
    if (newUser && newUser.id === userId) {
      setNewUser(prevNewUser => ({
        ...prevNewUser!,
        [field]: value
      }));
    }
  };

  const handleRolesChange = (e: React.ChangeEvent<HTMLSelectElement>, userId: string) => {
    const selectedRole = e.target.value;
    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === userId ? { ...user, roles: [selectedRole] } : user
      )
    );
    if (newUser && newUser.id === userId) {
      setNewUser(prevNewUser => ({
        ...prevNewUser!,
        roles: [selectedRole]
      }));
    }
  };

  return (
    <div className="user-management-container">
      <div className="user-management-header">
        <h1>User Management</h1>
        <button className="add-user-btn" onClick={handleAddUser}>Add New User</button>
      </div>
      <div className="user-management-table-container">
        <table className="user-management-table">
          <thead>
            <tr>
              <th>Email</th>
              <th>Full Name</th>
              <th>Active</th>
              <th>Superuser</th>
              <th>Roles</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {newUser && editingUserId === "new" && (
              <tr>
                <td><input type="email" value={newUser.email || ''} onChange={(e) => handleChange(e, 'email', "new")} /></td>
                <td><input type="text" value={newUser.full_name || ''} onChange={(e) => handleChange(e, 'full_name', "new")} /></td>
                <td><input type="checkbox" checked={newUser.is_active || false} onChange={(e) => handleChange(e, 'is_active', "new")} /></td>
                <td><input type="checkbox" checked={newUser.is_superuser || false} onChange={(e) => handleChange(e, 'is_superuser', "new")} /></td>
                <td>
                  <select value={newUser.roles?.[0] || 'user'} onChange={(e) => handleRolesChange(e, "new")}>
                    <option value="user">user</option>
                    <option value="admin">admin</option>
                    <option value="staff-admin">staff-admin</option>
                    <option value="staff">staff</option>
                  </select>
                </td>
                <td>
                  <button onClick={() => handleSave(newUser as User)}>Save</button>
                  <button onClick={handleCancel}>Cancel</button>
                </td>
              </tr>
            )}
            {users.map((user) => (
              <tr key={user.id}>
                {editingUserId === user.id ? (
                  <>
                    <td><input type="email" value={user.email} onChange={(e) => handleChange(e, 'email', user.id)} /></td>
                    <td><input type="text" value={user.full_name} onChange={(e) => handleChange(e, 'full_name', user.id)} /></td>
                    <td><input type="checkbox" checked={user.is_active} onChange={(e) => handleChange(e, 'is_active', user.id)} /></td>
                    <td><input type="checkbox" checked={user.is_superuser} onChange={(e) => handleChange(e, 'is_superuser', user.id)} /></td>
                    <td>
                      <select value={user.roles?.[0] || 'user'} onChange={(e) => handleRolesChange(e, user.id)}>
                        <option value="user">user</option>
                        <option value="admin">admin</option>
                        <option value="staff-admin">staff-admin</option>
                        <option value="staff">staff</option>
                      </select>
                    </td>
                    <td>
                      <button onClick={() => handleSave(user)}>Save</button>
                      <button onClick={handleCancel}>Cancel</button>
                    </td>
                  </>
                ) : (
                  <>
                    <td>{user.email}</td>
                    <td>{user.full_name}</td>
                    <td>{user.is_active ? 'Yes' : 'No'}</td>
                    <td>{user.is_superuser ? 'Yes' : 'No'}</td>
                    <td>{user.roles.join(', ')}</td>
                    <td>
                      <button onClick={() => handleEdit(user)}>Edit</button>
                      <button onClick={() => handleDelete(user.id)}>Delete</button>
                    </td>
                  </>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default UserManagement; 