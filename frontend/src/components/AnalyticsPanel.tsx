import React, { useEffect, useState } from "react";
import { useAppStore } from "../store";
import { fetchTokenUsage, fetchChatHistory } from "../api/analytics";

const AnalyticsPanel: React.FC = () => {
  const analytics = useAppStore((s) => s.analytics);
  const setAnalytics = useAppStore((s) => s.setAnalytics);
  const chatHistory = useAppStore((s) => s.chatHistory);
  const setChatHistory = useAppStore((s) => s.setChatHistory);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    setLoading(true);
    Promise.all([
      fetchTokenUsage(),
      fetchChatHistory({ limit: 10 }),
    ])
      .then(([usage, history]) => {
        setAnalytics(usage);
        setChatHistory(history);
        setError("");
      })
      .catch(() => setError("Failed to load analytics."))
      .finally(() => setLoading(false));
  }, [setAnalytics, setChatHistory]);

  return (
    <div style={{ background: "#23252b", borderRadius: 12, padding: 24, margin: 24 }}>
      <h3 style={{ color: "#fff", marginBottom: 16 }}>Analytics & History</h3>
      {loading ? (
        <div>Loading...</div>
      ) : error ? (
        <div style={{ color: "#f87171" }}>{error}</div>
      ) : (
        <>
          <div style={{ marginBottom: 16 }}>
            <strong>Total Requests:</strong> {analytics?.total_requests || 0}
            <br />
            <strong>Total Tokens:</strong> {analytics?.total_tokens || 0}
          </div>
          <div>
            <strong>Recent Chats:</strong>
            <ul style={{ margin: 0, padding: 0, listStyle: "none" }}>
              {chatHistory.map((c, i) => (
                <li key={i} style={{ marginBottom: 8, color: "#b0b3b8" }}>
                  <span style={{ color: "#fff" }}>{c.prompt}</span>
                  <span style={{ marginLeft: 8, fontSize: 12 }}>
                    ({new Date(c.timestamp).toLocaleString()})
                  </span>
                </li>
              ))}
            </ul>
          </div>
        </>
      )}
    </div>
  );
};

export default AnalyticsPanel; 