import React, { useState } from "react";
import { useAppStore } from "../store";
import api from "../api";
import { sendWebSocketMessage } from "../ws";

const ChatFooter: React.FC = () => {
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const { llm, project, prompt, systemPrompt, addChatMessage, connectionStatus, clearChat } = useAppStore();

  const handleSend = async () => {
    if (!input.trim() || loading) return;
    const userMsg = {
      id: Date.now().toString(),
      sender: "user" as const,
      content: input,
      timestamp: new Date().toISOString(),
    };
    addChatMessage(userMsg);
    setInput("");
    setLoading(true);
    // Try WebSocket first
    const wsSent =
      connectionStatus === "CONNECTED" &&
      sendWebSocketMessage({
        message: input,
        model_id: llm,
        system_prompt: systemPrompt,
        parameters: {},
      });
    if (!wsSent) {
      try {
        const res = await api.post("/chat/", {
          message: input,
          model_id: llm,
          system_prompt: systemPrompt,
          parameters: {},
        });
        addChatMessage({
          id: Date.now().toString() + "-ai",
          sender: "ai",
          content: res.data.response,
          timestamp: new Date().toISOString(),
        });
      } catch (err: any) {
        addChatMessage({
          id: Date.now().toString() + "-err",
          sender: "ai",
          content: err?.response?.data?.detail || "Error contacting AI backend.",
          timestamp: new Date().toISOString(),
        });
      }
    }
    setLoading(false);
  };

  return (
    <footer className="chat-footer">
      <input
        type="text"
        className="chat-input"
        placeholder="Provide me patnum :)"
        value={input}
        onChange={(e) => setInput(e.target.value)}
        onKeyPress={(e) => { if (e.key === "Enter") handleSend(); }}
        disabled={loading}
      />
      <button className="send-btn" onClick={handleSend} disabled={loading}>
        {loading ? <span className="loader" /> : ">"}
      </button>
    </footer>
  );
};

export default ChatFooter; 