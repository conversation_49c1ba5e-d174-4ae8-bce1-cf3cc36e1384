import React from "react";
import { useAppStore } from "../store";
import { login, logout } from "../auth";

const Header: React.FC = () => {
  const user = useAppStore((s) => s.user);
  return (
    <header className="app-header">
      <div className="app-title">
        <span role="img" aria-label="chat">💬</span>
        <span>RPX Play Ground</span>
      </div>
      <div className="connection-status">
        {user ? (
          <>
            <span style={{ marginRight: 16 }}>{user.name}</span>
            <button onClick={logout} className="auth-btn">Logout</button>
          </>
        ) : (
          <button onClick={login} className="auth-btn">Login with Microsoft</button>
        )}
      </div>
    </header>
  );
};

export default Header; 