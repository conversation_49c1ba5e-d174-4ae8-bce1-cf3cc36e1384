import React from 'react';
import { login } from '../auth';

interface LoginProps {
  authErrorMessage: string | null;
}

const Login: React.FC<LoginProps> = ({ authErrorMessage }) => {
  return (
    <div className="login-container">
      <div className="login-box">
        <h1>Welcome to RPX Playground</h1>
        <p>Please sign in to continue</p>
        {authErrorMessage && <p className="auth-error-message">{authErrorMessage}</p>}
        <button onClick={login} className="microsoft-login-btn">
          <img 
            src="https://upload.wikimedia.org/wikipedia/commons/9/96/Microsoft_logo_%282012%29.svg" 
            alt="Microsoft Logo" 
            className="microsoft-logo"
          />
          Sign in with Microsoft
        </button>
      </div>
    </div>
  );
};

export default Login; 