import React, { useEffect, useState } from 'react';
import { useAppStore } from '../store';
import { fetchPrompts } from '../api/prompts';

const Dashboard: React.FC = () => {
  const { prompts, setPrompts } = useAppStore();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPrompts = async () => {
      try {
        setLoading(true);
        const data = await fetchPrompts();
        setPrompts(data);
        setError(null);
      } catch (err) {
        setError('Failed to load prompts');
        console.error('Error loading prompts:', err);
      } finally {
        setLoading(false);
      }
    };

    loadPrompts();
  }, [setPrompts]);

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h1>Prompts</h1>
        <button className="new-prompt-btn">New prompt</button>
      </div>
      <div className="dashboard-controls">
        <div className="filters-dropdown">
          <button className="filters-btn">Filters <span className="dropdown-arrow">▼</span></button>
        </div>
      </div>
      <div className="prompts-table-container">
        <table className="prompts-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Model</th>
              <th>Type</th>
              <th>Created At</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={6} style={{ textAlign: 'center' }}>Loading...</td>
              </tr>
            ) : error ? (
              <tr>
                <td colSpan={6} style={{ textAlign: 'center', color: '#dc3545' }}>{error}</td>
              </tr>
            ) : prompts.length === 0 ? (
              <tr>
                <td colSpan={6} style={{ textAlign: 'center' }}>No prompts to display</td>
              </tr>
            ) : (
              prompts.map((prompt) => (
                <tr key={prompt.id}>
                  <td>{prompt.title}</td>
                  <td>{prompt.model}</td>
                  <td>{prompt.is_template ? 'Template' : 'Regular'}</td>
                  <td>{new Date(prompt.created_at || '').toLocaleDateString()}</td>
                  <td>{prompt.is_public ? 'Public' : 'Private'}</td>
                  <td>
                    <button className="action-btn">Edit</button>
                    <button className="action-btn">Delete</button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      <div className="pagination-controls">
        <span>Rows per page:</span>
        <select>
          <option>50</option>
          <option>100</option>
        </select>
        <span>Page 1 of 1</span>
        <button>&lt;&lt;</button>
        <button>&lt;</button>
        <button>&gt;</button>
        <button>&gt;&gt;</button>
      </div>
    </div>
  );
};

export default Dashboard; 