import React, { useState, useEffect } from 'react';
import { Box, Grid, Paper, Typography, TextField, Button, Select, MenuItem, FormControl, InputLabel, Checkbox, FormControlLabel } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { playgroundService } from '../services/playground';
import { Prompt, Project, PatentData, PlaygroundState } from '../types/playground';

const Playground: React.FC = () => {
    const [state, setState] = useState<PlaygroundState>({
        selectedPrompt: '',
        selectedVersion: '',
        latestPrompt: '',
        latestPromptBox: '',
        inputBox: '',
        promptsAvailable: ['-- Select a prompt --'],
        gptVersion: 'gpt-4o-mini',
        userPrompt: [],
        publish: false
    });

    const [projects, setProjects] = useState<string[]>([]);
    const [prompts, setPrompts] = useState<Prompt[]>([]);
    const [patentData, setPatentData] = useState<PatentData[]>([]);
    const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
    const [patentInput, setPatentInput] = useState<string>('');

    useEffect(() => {
        loadProjects();
        loadPrompts();
    }, []);

    const loadProjects = async () => {
        try {
            const projectList = await playgroundService.getProjects();
            setProjects(projectList);
        } catch (error) {
            console.error('Error loading projects:', error);
        }
    };

    const loadPrompts = async () => {
        try {
            const promptList = await playgroundService.getPrompts();
            setPrompts(promptList);
            setState(prev => ({
                ...prev,
                promptsAvailable: ['-- Select a prompt --', ...promptList.map(p => p.name)]
            }));
        } catch (error) {
            console.error('Error loading prompts:', error);
        }
    };

    const handlePromptChange = async (promptName: string) => {
        if (promptName === '-- Select a prompt --') {
            setState(prev => ({
                ...prev,
                selectedPrompt: '',
                latestPrompt: '',
                inputBox: ''
            }));
            return;
        }

        try {
            const { prompt, version } = await playgroundService.getLatestPrompt(promptName);
            setState(prev => ({
                ...prev,
                selectedPrompt: promptName,
                latestPrompt: prompt,
                latestPromptBox: prompt,
                inputBox: promptName
            }));
        } catch (error) {
            console.error('Error loading prompt:', error);
        }
    };

    const handleVersionChange = async (version: string) => {
        if (!state.selectedPrompt) return;

        try {
            const versionNum = parseInt(version.split(' - ')[0]);
            const { prompt, labels } = await playgroundService.getPromptVersion(state.selectedPrompt, versionNum);
            setState(prev => ({
                ...prev,
                selectedVersion: version,
                latestPrompt: prompt,
                latestPromptBox: prompt
            }));
        } catch (error) {
            console.error('Error loading version:', error);
        }
    };

    const handleSavePrompt = async () => {
        if (!state.latestPromptBox || !state.inputBox) return;

        try {
            const dt = new Date().toISOString().replace(/[:.]/g, '_');
            const labels = [dt, 'latest'];
            if (state.publish) {
                labels.push('production');
            }

            await playgroundService.createPrompt({
                name: state.inputBox,
                prompt: state.latestPromptBox,
                version: 1,
                labels,
                created_at: new Date().toISOString(),
                is_production: state.publish
            });

            await loadPrompts();
            setState(prev => ({ ...prev, publish: false }));
        } catch (error) {
            console.error('Error saving prompt:', error);
        }
    };

    const handlePatentSubmit = async (patnums: string[]) => {
        try {
            const data = await playgroundService.getPatents(patnums);
            setPatentData(data);
        } catch (error) {
            console.error('Error fetching patents:', error);
        }
    };

    return (
        <Box sx={{ flexGrow: 1, p: 3 }}>
            <Grid container spacing={3}>
                <Grid item xs={8}>
                    <Paper sx={{ p: 2 }}>
                        <Typography variant="h5" gutterBottom>
                            💬 Prompt Playground
                        </Typography>
                        <TextField
                        fullWidth
                        label="Enter patent numbers (comma separated)"
                        value={patentInput}
                        onChange={(e) => setPatentInput(e.target.value)}
                        margin="normal"
                        />
                        <Box sx={{ mt: 2 }}>
                        <Button variant="contained" onClick={() => {
                        const patnums = patentInput.split(',').map(p => p.trim()).filter(p => p);
                        handlePatentSubmit(patnums);
                        }}>
                        Run
                        </Button>
                        </Box>

                        <TextField
                            fullWidth
                            multiline
                            rows={4}
                            value={state.latestPromptBox}
                            onChange={(e) => setState(prev => ({ ...prev, latestPromptBox: e.target.value }))}
                            label="Edit Prompt"
                            margin="normal"
                        />

                        <Box sx={{ mt: 2 }}>
                            <Button variant="contained" onClick={handleSavePrompt}>
                                Save
                            </Button>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={state.publish}
                                        onChange={(e) => setState(prev => ({ ...prev, publish: e.target.checked }))}
                                    />
                                }
                                label="Publish to Production"
                            />
                        </Box>
                    </Paper>
                </Grid>

                <Grid item xs={4}>
                    <Paper sx={{ p: 2 }}>
                        <Typography variant="h6" gutterBottom>
                            Settings
                        </Typography>

                        <FormControl fullWidth margin="normal">
                            <InputLabel>Select an LLM</InputLabel>
                            <Select
                                value={state.gptVersion}
                                onChange={(e) => setState(prev => ({ ...prev, gptVersion: e.target.value }))}
                            >
                                <MenuItem value="gpt-4o-mini">gpt-4o-mini</MenuItem>
                                <MenuItem value="gpt-4o">gpt-4o</MenuItem>
                                <MenuItem value="o1-mini">o1-mini</MenuItem>
                                <MenuItem value="o1">o1</MenuItem>
                                <MenuItem value="o3-mini">o3-mini</MenuItem>
                                <MenuItem value="claude-3-7-sonnet-latest">claude-3-7-sonnet-latest</MenuItem>
                                <MenuItem value="claude-3-5-sonnet-latest">claude-3-5-sonnet-latest</MenuItem>
                                <MenuItem value="claude-3-5-haiku-latest">claude-3-5-haiku-latest</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl fullWidth margin="normal">
                            <InputLabel>Select Prompt</InputLabel>
                            <Select
                                value={state.selectedPrompt}
                                onChange={(e) => handlePromptChange(e.target.value)}
                            >
                                {state.promptsAvailable.map((prompt) => (
                                    <MenuItem key={prompt} value={prompt}>
                                        {prompt}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Paper>
                </Grid>

                {patentData.length > 0 && (
                    <Grid item xs={12}>
                        <Paper sx={{ p: 2 }}>
                            <Typography variant="h6" gutterBottom>
                                Patent Data
                            </Typography>
                            <DataGrid
                                rows={patentData}
                                columns={Object.keys(patentData[0]).map(key => ({
                                    field: key,
                                    headerName: key,
                                    width: 200
                                }))}
                                autoHeight
                                pageSize={5}
                            />
                        </Paper>
                    </Grid>
                )}
            </Grid>
        </Box>
    );
};

export default Playground; 