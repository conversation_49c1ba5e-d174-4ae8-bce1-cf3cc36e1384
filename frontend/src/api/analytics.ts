import api from '../api';

export async function fetchTokenUsage(days = 30) {
  const res = await api.get('/analytics/usage', { params: { days } });
  return res.data;
}

export async function fetchChatHistory({ skip = 0, limit = 50, model_id, start_date, end_date }: any = {}) {
  const res = await api.get('/analytics/history', {
    params: { skip, limit, model_id, start_date, end_date },
  });
  return res.data;
}

export async function fetchGlobalAnalytics(days = 30) {
  const res = await api.get('/analytics/global', { params: { days } });
  return res.data;
} 