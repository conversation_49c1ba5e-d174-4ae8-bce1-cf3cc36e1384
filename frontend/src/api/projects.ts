import api from '../api';

export interface Project {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface ProjectCreate {
  name: string;
  description?: string;
}

export async function fetchProjects(): Promise<string[]> {
  try {
    const res = await api.get<string[]>('/prompts/projects');
    return res.data;
  } catch (error) {
    console.error('Error fetching projects:', error);
    // Return fallback projects if API fails
    return ['WiFi-6', 'Project X'];
  }
}

export async function createProject(project: ProjectCreate): Promise<{ project_id: string }> {
  const res = await api.post<{ project_id: string }>('/prompts/projects', project);
  return res.data;
}

export async function fetchPromptsByProject(projectName: string) {
  try {
    const res = await api.get(`/prompts/by-project/${encodeURIComponent(projectName)}`);
    return res.data;
  } catch (error) {
    console.error('Error fetching prompts by project:', error);
    // Fallback to all prompts if project-specific endpoint fails
    const allPromptsRes = await api.get('/prompts/');
    return allPromptsRes.data;
  }
}
