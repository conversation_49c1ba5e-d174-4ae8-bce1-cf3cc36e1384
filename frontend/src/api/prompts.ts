import api from '../api';

export interface Prompt {
  id: string;
  title: string;
  content: string;
  model: string;
  system_prompt?: string;
  is_template?: boolean;
  is_public?: boolean;
  created_by?: string;
  created_at?: string;
  updated_at?: string;
}

export async function fetchPrompts() {
  const res = await api.get<Prompt[]>('/prompts/');
  return res.data;
}

export async function addPrompt(prompt: Partial<Prompt>) {
  const res = await api.post<Prompt>('/prompts/', prompt);
  return res.data;
}

export async function publishPrompt(promptId: string) {
  // This assumes publishing is an update to is_public=true
  const res = await api.put<Prompt>(`/prompts/${promptId}`, { is_public: true });
  return res.data;
} 