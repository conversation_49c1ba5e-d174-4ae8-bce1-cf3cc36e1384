import api from '../api';

export interface LLMModel {
  id: string;
  name: string;
  provider: string;
  description: string;
  input_modalities: string[];
  output_modalities: string[];
}

export async function fetchBedrockModels(): Promise<LLMModel[]> {
  try {
    const res = await api.get<LLMModel[]>('/models/bedrock');
    return res.data;
  } catch (error) {
    console.error('Error fetching Bedrock models:', error);
    // Return fallback models if API fails
    return [
      {
        id: 'anthropic.claude-3-sonnet-20240229-v1:0',
        name: 'Claude 3 Sonnet',
        provider: 'Anthropic',
        description: 'Anthropic - Claude 3 Sonnet',
        input_modalities: ['TEXT'],
        output_modalities: ['TEXT']
      },
      {
        id: 'anthropic.claude-3-haiku-20240307-v1:0',
        name: 'Claude 3 Haiku',
        provider: 'Anthropic',
        description: 'Anthropic - Claude 3 Haiku',
        input_modalities: ['TEXT'],
        output_modalities: ['TEXT']
      },
      {
        id: 'amazon.titan-text-express-v1',
        name: 'Titan Text Express',
        provider: 'Amazon',
        description: 'Amazon - Titan Text Express',
        input_modalities: ['TEXT'],
        output_modalities: ['TEXT']
      }
    ];
  }
}
