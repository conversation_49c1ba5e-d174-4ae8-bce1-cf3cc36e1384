import axios from 'axios';

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1',
});

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    // If the error is 401 Unauthorized and it's not the login/callback route
    // and we haven't already retried this request
    if (error.response.status === 401 && !originalRequest._retry &&
        !originalRequest.url.includes('/auth/login') && !originalRequest.url.includes('/auth/callback')) {
      originalRequest._retry = true; // Mark as retried
      // In a real application, you might attempt to refresh the token here
      // For now, we'll just clear the token and redirect to login
      localStorage.removeItem('access_token');
      window.location.href = '/'; // Redirect to login page
    }
    return Promise.reject(error);
  }
);

export default api; 