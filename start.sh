#!/bin/bash

# Function to handle Ctrl+C
cleanup() {
    echo "Stopping all services..."
    ./stop.sh
    exit 0
}

# Set up trap for Ctrl+C
trap cleanup SIGINT

# Start the backend
echo "Starting FastAPI backend..."
cd "$(dirname "$0")"
virtual_env_path="./venv"
"$virtual_env_path"/bin/uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!

# Start the frontend
echo "Starting React frontend..."
cd frontend
npm install
npm run dev &
FRONTEND_PID=$!

# Wait for either process to exit
wait $BACKEND_PID $FRONTEND_PID