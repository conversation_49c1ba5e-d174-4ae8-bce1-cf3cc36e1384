# AI Prompt Playground

An enterprise-grade conversational AI platform that integrates with AWS Bedrock and SOLR search, featuring Microsoft SSO authentication and comprehensive analytics.

## Features

- Microsoft SSO authentication
- AWS Bedrock integration
- SOLR search integration
- MongoDB for data storage
- FastAPI backend
- Modern, responsive UI
- Comprehensive analytics
- Prompt template management
- Role-based access control

## Prerequisites

- Python 3.8+
- MongoDB
- AWS Account with Bedrock access
- Microsoft Azure AD application
- SOLR instance

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ai-prompt-playground
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Create a `.env` file:
```bash
cp .env.example .env
```

5. Update the `.env` file with your configuration:
- Microsoft SSO credentials
- AWS credentials
- MongoDB connection string
- SOLR URL

## Running the Application

1. Start the backend server:
```bash
uvicorn app.main:app --reload
```

2. Access the API documentation at:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Project Structure

```
ai-prompt-playground/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── __init__.py
│   │       └── prompts.py
│   ├── core/
│   │   └── auth.py
│   ├── db/
│   │   └── mongodb.py
│   ├── models/
│   │   ├── prompt.py
│   │   └── user.py
│   ├── __init__.py
│   ├── config.py
│   └── main.py
├── .env.example
├── .gitignore
├── README.md
├── requirements.txt
└── setup.sh
```

## API Endpoints

### Prompts
- `POST /api/v1/prompts/` - Create a new prompt
- `GET /api/v1/prompts/` - List prompts
- `GET /api/v1/prompts/{prompt_id}` - Get a specific prompt
- `PUT /api/v1/prompts/{prompt_id}` - Update a prompt
- `DELETE /api/v1/prompts/{prompt_id}` - Delete a prompt

## Security

- All endpoints require authentication via Microsoft SSO
- Role-based access control for prompt management
- Secure storage of credentials and API keys
- HTTPS encryption for all communications

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.