# Application
DEBUG=true  # Set to True for development, False for production

# Security
SECRET_KEY=f1917b63f58e137190e2908709e3e34b7f7e2501a303e6d6b1d12c6a08601614
ALGORITHM=HS256  # Used for JWT signing and session security
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Microsoft SSO

MICROSOFT_CLIENT_ID=7ddcae72-1d6b-43f7-8525-4a8b1953df7f  # Azure AD App registration client ID
MICROSOFT_CLIENT_SECRET=ac9dcc62-4985-445e-b961-cc74aac18788  # Azure AD App registration secret
MICROSOFT_TENANT_ID=48b5ca84-91a0-4c16-a12e-cd0934eeca24  # Azure AD tenant ID
MICROSOFT_REDIRECT_URI=http://localhost:8000/api/v1/auth/callback  # Redirect URI for SSO

# AWS
AWS_ACCESS_KEY_ID=  # AWS credentials for Bedrock
AWS_SECRET_ACCESS_KEY=  # AWS credentials for Bedrock
AWS_REGION=us-east-1  # AWS region for Bedrock

# MongoDB
MONGODB_URL=mongodb://localhost:27017  # MongoDB connection string
MONGODB_DB_NAME=ai_prompt_playground  # MongoDB database name

# SOLR
SOLR_URL=http://localhost:8983/solr  # SOLR endpoint

# Redis (for rate limiting and caching)
REDIS_URL=redis://localhost:6379/0  # Redis connection string

# Domain restriction (optional)
ALLOWED_DOMAIN=yourcompany.com  # Only allow SSO from this domain
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_NAME=Admin User