#!/bin/bash

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
    echo "Please update the .env file with your configuration"
fi

echo "Setup complete! Don't forget to:"
echo "1. Update the .env file with your configuration"
echo "2. Activate the virtual environment with: source venv/bin/activate"
echo "3. Run the application with: uvicorn app.main:app --reload" 